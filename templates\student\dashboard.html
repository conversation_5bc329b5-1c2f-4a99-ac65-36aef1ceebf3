{% extends "student/base.html" %}

{% block title %}Student Dashboard{% endblock %}

{% block content %}
<style>
    .dashboard-header {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        text-align: center;
    }
    .dashboard-header h1 {
        color: #333;
        font-size: 2rem;
        margin-bottom: 10px;
        font-weight: 700;
    }
    .dashboard-header p {
        color: #666;
        font-size: 1.1rem;
        margin: 0;
    }
    .welcome-card {
        background: linear-gradient(135deg, #1976D2, #00BCD4);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
    }
    .welcome-card h2 {
        font-size: 1.5rem;
        margin-bottom: 10px;
        font-weight: 600;
    }
    .welcome-card p {
        font-size: 1.1rem;
        margin: 0;
        opacity: 0.9;
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        transition: transform 0.3s ease;
    }
    .stat-card:hover {
        transform: translateY(-5px);
    }
    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        font-size: 1.5rem;
    }
    .stat-content h3 {
        font-size: 2rem;
        margin: 0;
        font-weight: 700;
    }
    .stat-content p {
        margin: 0;
        font-size: 1rem;
        font-weight: 600;
    }
    .pending-icon { background: #fff3cd; color: #856404; }
    .approved-icon { background: #d4edda; color: #155724; }
    .rejected-icon { background: #f8d7da; color: #721c24; }

    /* Real-time indicator */
    .real-time-indicator {
        font-size: 0.8rem;
        color: #28a745;
        animation: pulse 2s infinite;
        margin-left: 10px;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
</style>

<div class="dashboard-header">
    <h1>{{ translations.dashboard }}
        <span id="realTimeIndicator" class="real-time-indicator" title="Real-time updates every 1 second">🔄</span>
    </h1>
    <p>{{ translations.welcome }}</p>
</div>

<!-- Welcome Section -->
<div class="welcome-card">
    <h2>{{ translations.welcome }}, {{ session.name }}!</h2>
    <p>{{ translations.department }}: {{ department }}</p>
</div>

<!-- Request Counts Section -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon pending-icon">
            <i class="fas fa-clock"></i>
        </div>
        <div class="stat-content">
            <h3>{{ pending_count }}</h3>
            <p>{{ translations.pending }}</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon approved-icon">
            <i class="fas fa-check"></i>
        </div>
        <div class="stat-content">
            <h3>{{ approved_count }}</h3>
            <p>{{ translations.approved }}</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon rejected-icon">
            <i class="fas fa-times"></i>
        </div>
        <div class="stat-content">
            <h3>{{ rejected_count }}</h3>
            <p>{{ translations.rejected }}</p>
        </div>
    </div>
</div>



<!-- Recent Requests -->
<style>
    .recent-requests {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    .recent-requests-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
    }
    .recent-requests h3 {
        color: #333;
        font-size: 1.4rem;
        margin: 0;
        font-weight: 600;
    }
    .clear-all-btn {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .clear-all-btn:hover {
        background: linear-gradient(135deg, #c82333, #bd2130);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    }
    .clear-all-btn i {
        font-size: 0.8rem;
    }
    .requests-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .requests-table th {
        background: #f8f9fa;
        color: #333;
        padding: 15px 12px;
        text-align: left;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
        font-size: 0.9rem;
    }
    .requests-table td {
        padding: 15px 12px;
        border-bottom: 1px solid #e9ecef;
        color: #495057;
        vertical-align: middle;
    }
    .requests-table tr:hover {
        background: #f8f9fa;
    }
    .status-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
    }
    .status-pending {
        background: #fff3cd;
        color: #856404;
    }
    .status-approved {
        background: #d4edda;
        color: #155724;
    }
    .status-rejected {
        background: #f8d7da;
        color: #721c24;
    }
    .no-requests {
        text-align: center;
        padding: 40px;
        color: #666;
    }
    .no-requests i {
        font-size: 3rem;
        color: #ccc;
        margin-bottom: 15px;
    }
    .status-explanation {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
        font-size: 0.9rem;
        color: #666;
    }
    .status-explanation h4 {
        color: #333;
        font-size: 1rem;
        margin-bottom: 10px;
        font-weight: 600;
    }
    .status-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
    }
    .status-item img {
        width: 20px;
        height: 20px;
        margin-right: 10px;
    }
</style>

<div class="recent-requests">
    <div class="recent-requests-header">
        <h3>{{ translations.recent_requests }}</h3>
        {% if requests %}
        <button type="button" class="clear-all-btn" onclick="showClearAllModal()" id="clearAllBtn">
            <i class="fas fa-trash-alt"></i>
            Clear All
        </button>
        {% endif %}
    </div>
    {% if requests %}
        <div class="table-responsive">
            <table class="requests-table">
                <thead>
                    <tr>
                        <th>{{ translations.request_id }}</th>
                        <th>{{ translations.academic_years }}</th>
                        <th>{{ translations.status }}</th>
                        <th>{{ translations.date }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for request in requests %}
                    <tr>
                        <td><strong>{{ request.id }}</strong></td>
                        <td>{{ request.academic_years|join(', ') }}</td>
                        <td>
                            {% if request.status == 'pending_finance' or request.status == 'pending_confirmation' %}
                                <div class="status-icon status-pending">
                                    <img src="{{ url_for('static', filename='images/13.png') }}" alt="Pending" style="width: 16px; height: 16px;">
                                </div>
                                {{ translations.pending }}
                            {% elif request.status == 'approved_finance' or request.status == 'completed' %}
                                <div class="status-icon status-approved">
                                    <img src="{{ url_for('static', filename='images/approved.png') }}" alt="Approved" style="width: 16px; height: 16px;">
                                </div>
                                {{ translations.approved }}
                            {% elif request.status == 'rejected' %}
                                <div class="status-icon status-rejected">
                                    <img src="{{ url_for('static', filename='images/rejected.png') }}" alt="Rejected" style="width: 16px; height: 16px;">
                                </div>
                                {{ translations.rejected }}
                            {% else %}
                                <div class="status-icon status-pending">
                                    <img src="{{ url_for('static', filename='images/13.png') }}" alt="Pending" style="width: 16px; height: 16px;">
                                </div>
                                {{ translations.pending }}
                            {% endif %}
                        </td>
                        <td>{{ request.date }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Status Icons Explanation -->
        <div class="status-explanation">
            <h4>{{ translations.status_icons_explanation or 'Status Icons Explanation' }}</h4>
            <div class="status-item">
                <img src="{{ url_for('static', filename='images/13.png') }}" alt="Pending">
                <span>{{ translations.pending_explanation or 'Pending - Request is being processed by finance' }}</span>
            </div>
            <div class="status-item">
                <img src="{{ url_for('static', filename='images/approved.png') }}" alt="Approved">
                <span>{{ translations.approved_explanation or 'Approved - Request has been approved and sent to faculty' }}</span>
            </div>
            <div class="status-item">
                <img src="{{ url_for('static', filename='images/rejected.png') }}" alt="Rejected">
                <span>{{ translations.rejected_explanation or 'Rejected - Request has been rejected by finance' }}</span>
            </div>
        </div>
    {% else %}
        <div class="no-requests">
            <i class="fas fa-inbox"></i>
            <p>{{ translations.no_requests }}</p>
        </div>
    {% endif %}
</div>

<!-- Quick Actions -->
<style>
    .quick-actions {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    .quick-actions h3 {
        color: #333;
        font-size: 1.4rem;
        margin-bottom: 25px;
        font-weight: 600;
        text-align: center;
    }
    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    .action-btn {
        background: linear-gradient(135deg, #1976D2, #1565C0);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 20px;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        min-height: 80px;
    }
    .action-btn:hover {
        background: linear-gradient(135deg, #42A5F5, #1976D2);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
        color: white;
        text-decoration: none;
    }
    .action-btn i {
        margin-right: 10px;
        font-size: 1.2rem;
    }
    .action-btn.secondary {
        background: linear-gradient(135deg, #00BCD4, #0097A7);
    }
    .action-btn.secondary:hover {
        background: linear-gradient(135deg, #4DD0E1, #00BCD4);
    }
    .action-btn.success {
        background: linear-gradient(135deg, #4CAF50, #388E3C);
    }
    .action-btn.success:hover {
        background: linear-gradient(135deg, #66BB6A, #4CAF50);
    }
</style>

<div class="quick-actions">
    <h3>{{ translations.quick_actions }}</h3>
    <div class="actions-grid">
        <a href="{{ url_for('request_transcript') }}" class="action-btn">
            <i class="fas fa-file-alt"></i>{{ translations.request_transcript }}
        </a>
        <a href="{{ url_for('request_status') }}" class="action-btn secondary">
            <i class="fas fa-tasks"></i>{{ translations.request_status }}
        </a>
        <a href="{{ url_for('view_downloads') }}" class="action-btn success">
            <i class="fas fa-download"></i>{{ translations.view_downloads }}
        </a>
    </div>
</div>

<!-- Clear All Requests Modal -->
<div class="modal fade" id="clearAllModal" tabindex="-1" aria-labelledby="clearAllModalLabel" aria-hidden="true" style="z-index: 9999;">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
            <div class="modal-header" style="background: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                <h5 class="modal-title" id="clearAllModalLabel" style="color: #495057; font-weight: 600;">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    Clear All Recent Requests
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="font-size: 1.2rem;"></button>
            </div>
            <div class="modal-body" style="padding: 25px;">
                <div class="alert alert-warning" style="border-left: 4px solid #ffc107;">
                    <strong>⚠️ Warning:</strong> This action cannot be undone!
                </div>
                <p style="font-size: 1.1rem; margin-bottom: 15px;">Are you sure you want to delete <strong>approved, rejected, and downloaded requests</strong>?</p>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;">
                    <p class="mb-2"><strong>What will happen:</strong></p>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li><strong>Pending requests will be kept</strong> as reminders</li>
                        <li>Only approved, rejected, and downloaded requests will be deleted</li>
                        <li>This helps you remember ongoing requests while clearing completed ones</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer" style="background: #f8f9fa; border-top: 1px solid #dee2e6; padding: 15px 25px;">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="padding: 10px 20px; font-weight: 500;">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <form method="POST" action="{{ url_for('student_clear_all_requests') }}" style="display: inline; margin-left: 10px;">
                    <button type="submit" class="btn btn-danger" style="padding: 10px 20px; font-weight: 500;">
                        <i class="fas fa-trash-alt"></i> Clear All
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// Real-time dashboard updates using API endpoint
let refreshInterval;
let lastUpdateTime = null;
let lastDataHash = null;

function updateDashboardStats() {
    // Only refresh if page is visible and user is active
    if (document.hidden) return;

    console.log('🔄 Calling API for dashboard stats at', new Date().toLocaleTimeString());

    // Add subtle loading indicator
    const statsGrid = document.querySelector('.stats-grid');
    if (statsGrid) {
        statsGrid.style.opacity = '0.95';
    }

    fetch('/api/student/dashboard-stats', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('📤 Received API response:', data);

        if (data.error) {
            console.log('Dashboard stats error:', data.error);
            return;
        }

        // Check if data has changed using a simple hash
        const dataHash = `${data.pending_count}-${data.approved_count}-${data.rejected_count}`;
        if (lastDataHash && lastDataHash === dataHash) {
            // Data hasn't changed, just restore opacity and return
            const statsGrid = document.querySelector('.stats-grid');
            if (statsGrid) {
                statsGrid.style.opacity = '1';
            }
            return; // No changes
        }

        lastDataHash = dataHash;
        lastUpdateTime = data.last_updated;

        // Update the stat cards with animation
        updateStatCard('pending', data.pending_count);
        updateStatCard('approved', data.approved_count);
        updateStatCard('rejected', data.rejected_count);

        // Restore opacity
        const statsGrid = document.querySelector('.stats-grid');
        if (statsGrid) {
            statsGrid.style.opacity = '1';
        }

        // Flash the real-time indicator
        const indicator = document.getElementById('realTimeIndicator');
        if (indicator) {
            indicator.style.color = '#007bff';
            setTimeout(() => {
                indicator.style.color = '#28a745';
            }, 200);
        }

        console.log('🔄 Dashboard stats updated at', new Date().toLocaleTimeString());
    })
    .catch(error => {
        console.log('Dashboard stats refresh failed:', error);
        // Restore opacity on error
        const statsGrid = document.querySelector('.stats-grid');
        if (statsGrid) {
            statsGrid.style.opacity = '1';
        }
    });
}

function updateStatCard(type, newValue) {
    console.log(`🎯 updateStatCard called: type=${type}, newValue=${newValue}`);

    const statCards = document.querySelectorAll('.stat-card');
    let targetCard = null;

    // Find the correct stat card based on the icon or content
    statCards.forEach(card => {
        const icon = card.querySelector('.stat-icon i');
        if (type === 'pending' && icon && icon.classList.contains('fa-clock')) {
            targetCard = card;
        } else if (type === 'approved' && icon && icon.classList.contains('fa-check')) {
            targetCard = card;
        } else if (type === 'rejected' && icon && icon.classList.contains('fa-times')) {
            targetCard = card;
        }
    });

    if (targetCard) {
        const countElement = targetCard.querySelector('.stat-content h3');
        const currentValue = parseInt(countElement.textContent);

        console.log(`📊 ${type} card found: currentValue=${currentValue}, newValue=${newValue}`);

        if (currentValue !== newValue) {
            console.log(`✅ Updating ${type} count from ${currentValue} to ${newValue}`);

            // Animate the change
            targetCard.style.transform = 'scale(1.05)';
            targetCard.style.transition = 'transform 0.3s ease';

            // Update the value
            countElement.textContent = newValue;

            // Add highlight effect
            targetCard.classList.add('stat-updated');

            // Reset animation
            setTimeout(() => {
                targetCard.style.transform = 'scale(1)';
                targetCard.classList.remove('stat-updated');
            }, 500);
        } else {
            console.log(`❌ No update needed for ${type} - values match`);
        }
    } else {
        console.log(`❌ Target card not found for type: ${type}`);
    }
}

function startAutoRefresh() {
    // Refresh every 1 second for real-time updates
    refreshInterval = setInterval(updateDashboardStats, 1000);
    console.log('Auto-refresh started for dashboard stats (1 second intervals)');
}

function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
        console.log('Auto-refresh stopped');
    }
}

// Start auto-refresh when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Check if Bootstrap is loaded
    if (typeof bootstrap === 'undefined') {
        console.error('❌ Bootstrap is not loaded! Modal functionality will not work.');
    } else {
        console.log('✅ Bootstrap is loaded successfully');
    }

    startAutoRefresh();

    // Stop refresh when page is hidden, restart when visible
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            stopAutoRefresh();
        } else {
            startAutoRefresh();
            // Immediate update when page becomes visible
            setTimeout(updateDashboardStats, 1000);
        }
    });

    // Stop refresh when user leaves page
    window.addEventListener('beforeunload', function() {
        stopAutoRefresh();
    });

    // Update stats immediately when returning from other pages
    window.addEventListener('focus', function() {
        setTimeout(updateDashboardStats, 500);
    });
});

// Clear All Requests Modal Function
function showClearAllModal() {
    console.log('🗑️ Clear All button clicked');

    const modalElement = document.getElementById('clearAllModal');
    if (!modalElement) {
        console.error('❌ Modal element not found');
        alert('Error: Modal not found. Please refresh the page and try again.');
        return;
    }

    console.log('✅ Modal element found, attempting to show modal');

    try {
        // Try Bootstrap 5 first
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const clearAllModal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',
                keyboard: false
            });
            clearAllModal.show();
            console.log('✅ Bootstrap 5 modal shown successfully');
            return;
        }

        // Try jQuery/Bootstrap 4 fallback
        if (typeof $ !== 'undefined' && $.fn.modal) {
            $(modalElement).modal('show');
            console.log('✅ jQuery modal shown successfully');
            return;
        }

        // Manual modal display fallback
        console.log('⚠️ No modal library available, showing manually');
        modalElement.style.display = 'block';
        modalElement.classList.add('show');
        modalElement.setAttribute('aria-hidden', 'false');

        // Add backdrop
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        backdrop.id = 'clearAllModalBackdrop';
        document.body.appendChild(backdrop);

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        console.log('✅ Manual modal display successful');

    } catch (error) {
        console.error('❌ Error showing modal:', error);
        // Final fallback to confirm dialog
        const confirmed = confirm(
            '⚠️ WARNING: This action cannot be undone!\n\n' +
            'Are you sure you want to delete approved, rejected, and downloaded requests?\n\n' +
            '• Pending requests will be kept as reminders\n' +
            '• Only approved, rejected, and downloaded requests will be deleted'
        );

        if (confirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ url_for("student_clear_all_requests") }}';
            document.body.appendChild(form);
            form.submit();
        }
    }
}

// Function to hide modal manually
function hideClearAllModal() {
    const modalElement = document.getElementById('clearAllModal');
    const backdrop = document.getElementById('clearAllModalBackdrop');

    if (modalElement) {
        modalElement.style.display = 'none';
        modalElement.classList.remove('show');
        modalElement.setAttribute('aria-hidden', 'true');
    }

    if (backdrop) {
        backdrop.remove();
    }

    // Restore body scroll
    document.body.style.overflow = '';
}

// Add event listeners when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Make sure modal buttons are clickable
    const modal = document.getElementById('clearAllModal');
    if (modal) {
        // Close button functionality
        const closeButtons = modal.querySelectorAll('[data-bs-dismiss="modal"], .btn-close');
        closeButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                hideClearAllModal();
            });
        });

        // Form submission
        const form = modal.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                console.log('Form submitted');
                // Let the form submit normally
            });
        }
    }

    // Backdrop click to close
    document.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'clearAllModalBackdrop') {
            hideClearAllModal();
        }
    });
});
</script>

<style>
/* Animation for updated stats */
.stat-updated {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    color: white !important;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3) !important;
}

.stat-updated .stat-icon {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

.stat-updated h3,
.stat-updated p {
    color: white !important;
}

/* Smooth transitions for stat cards */
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Clear All Modal Styling */
.modal-header .modal-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-footer form {
    margin: 0;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}
</style>
{% endblock %}
