{% extends "student/base.html" %}

{% block title %}{{ translations.request_transcript }}{% endblock %}

{% block content %}
<div class="dashboard-header">
    <h1>{{ translations.request_transcript }}</h1>
    <p>{{ translations.request_transcript_desc }}</p>
</div>

<div class="card">
    <div class="card-header">
        <h2>{{ translations.request_transcript }}</h2>
    </div>
    <div class="card-body">
        <form action="{{ url_for('request_transcript') }}" method="POST">
            <div class="form-group">
                <label>{{ translations.academic_years }}</label>
                <p class="form-hint">{{ translations.request_transcript_desc }}</p>

                <div class="checkbox-group">
                    {% for year in available_years %}
                        <div class="checkbox-item">
                            <input type="checkbox" id="year-{{ year }}" name="academic_years" value="{{ year }}-{{ year + 1 }}">
                            <label for="year-{{ year }}">{{ year }}-{{ year + 1 }}</label>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <div class="form-group">
                <label for="email">{{ translations.email }}</label>
                <div class="input-group">
                    <span class="input-icon"><i class="fas fa-envelope"></i></span>
                    <input type="email" id="email" name="email" value="{{ session.email }}" required>
                </div>
                <p class="form-hint">{{ translations.request_transcript_desc }}</p>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> {{ translations.submit }}
                </button>
            </div>
        </form>
    </div>
    <div class="card-footer">
        <p><i class="fas fa-info-circle"></i> {{ translations.transcript_cost_note }}</p>
    </div>
</div>

<script>
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        const checkboxes = document.querySelectorAll('input[name="academic_years"]:checked');

        if (checkboxes.length === 0) {
            event.preventDefault();
            alert('{{ translations.select_at_least_one_year }}');
        }
    });
</script>
{% endblock %}
