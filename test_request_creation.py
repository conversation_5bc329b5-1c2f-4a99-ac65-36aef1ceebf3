#!/usr/bin/env python3
"""
Test script to verify request creation and database connectivity
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from new_database_service import add_request, get_requests_by_student_id, get_finance_dashboard_data

def test_database_connection():
    """Test basic database connectivity"""
    print("🔍 Testing database connection...")
    try:
        # Try to get finance dashboard data
        data = get_finance_dashboard_data()
        print(f"✅ Database connection successful!")
        print(f"📊 Found {data['pending_count']} pending requests")
        print(f"📊 Found {data['approved_count']} approved requests")
        print(f"📊 Found {data['rejected_count']} rejected requests")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_request_creation():
    """Test creating a new request"""
    print("\n🔍 Testing request creation...")
    try:
        # Test data
        test_student_id = "STU001"  # Make sure this student exists
        test_academic_years = ["2023-2024"]
        test_payment_method = "mobile_money"
        test_total_price = 1000.0
        test_filename = "test_payment_proof.jpg"
        
        print(f"📝 Creating test request for student {test_student_id}")
        
        # Create request
        new_request = add_request(
            student_reg_no=test_student_id,
            academic_years=test_academic_years,
            payment_method=test_payment_method,
            total_price=test_total_price,
            purpose='Test Request',
            institution_name='INES-Ruhengeri',
            payment_proof_filename=test_filename
        )
        
        if new_request:
            print(f"✅ Request created successfully!")
            print(f"📋 Request ID: {new_request['id']}")
            print(f"📋 Request Number: {new_request['request_number']}")
            print(f"📋 Status: {new_request['status']}")
            
            # Verify the request was saved
            print(f"\n🔍 Verifying request was saved...")
            student_requests = get_requests_by_student_id(test_student_id)
            print(f"📊 Found {len(student_requests)} requests for student {test_student_id}")
            
            # Find our test request
            test_request_found = False
            for req in student_requests:
                if str(req['id']) == str(new_request['id']):
                    test_request_found = True
                    print(f"✅ Test request found in database!")
                    print(f"📋 Status: {req['status']}")
                    print(f"📋 Payment Method: {req['payment_method']}")
                    break
            
            if not test_request_found:
                print(f"❌ Test request not found in database!")
                return False
                
            return True
        else:
            print(f"❌ Request creation failed!")
            return False
            
    except Exception as e:
        print(f"❌ Request creation test failed: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

def test_finance_dashboard():
    """Test finance dashboard data retrieval"""
    print("\n🔍 Testing finance dashboard data...")
    try:
        data = get_finance_dashboard_data()
        
        print(f"📊 Pending requests: {len(data['pending_requests'])}")
        print(f"📊 Approved requests: {len(data['approved_requests'])}")
        print(f"📊 Rejected requests: {len(data['rejected_requests'])}")
        
        # Show some details of pending requests
        if data['pending_requests']:
            print(f"\n📋 Sample pending request:")
            req = data['pending_requests'][0]
            print(f"   - ID: {req['id']}")
            print(f"   - Student: {req['student_name']}")
            print(f"   - Amount: {req['total_amount']} RWF")
            print(f"   - Payment Method: {req['payment_method']}")
            print(f"   - Status: {req['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Finance dashboard test failed: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting INES Transcript System Tests")
    print("=" * 50)
    
    # Test 1: Database Connection
    db_test = test_database_connection()
    
    # Test 2: Request Creation
    request_test = test_request_creation()
    
    # Test 3: Finance Dashboard
    finance_test = test_finance_dashboard()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY:")
    print(f"   Database Connection: {'✅ PASS' if db_test else '❌ FAIL'}")
    print(f"   Request Creation: {'✅ PASS' if request_test else '❌ FAIL'}")
    print(f"   Finance Dashboard: {'✅ PASS' if finance_test else '❌ FAIL'}")
    
    if all([db_test, request_test, finance_test]):
        print("\n🎉 All tests passed! The system is working correctly.")
        return True
    else:
        print("\n⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
