# INES Transcript System - MySQL Database Requirements
# Additional dependencies for MySQL database migration

# Core Flask and Database
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5

# MySQL Database Driver
PyMySQL==1.1.0
cryptography==41.0.4

# Password Hashing
bcrypt==4.0.1

# Existing dependencies (keep these)
Flask-Mail==0.9.1
sendgrid==6.10.0
python-dotenv==1.0.0
Werkzeug==2.3.7

# Development and Testing
pytest==7.4.2
pytest-flask==1.2.0

# Optional: Database Administration
mysqlclient==2.2.0  # Alternative to PyMySQL (requires MySQL dev libraries)

# Optional: Database Connection Pooling
SQLAlchemy-Utils==0.41.1

# Optional: Database Backup and Migration Tools
alembic==1.12.0  # Included with Flask-Migrate but can be standalone
