<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finance Dashboard | INES-Ruhengeri Transcript System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Enhanced Finance Dashboard Styles */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
            background: #f5f7fa;
        }

        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #083464, #1976D2);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }

        .sidebar-header {
            text-align: center;
            padding: 30px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .profile-image {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid rgba(255, 255, 255, 0.3);
            margin-bottom: 15px;
        }

        .sidebar-menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 5px 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-left-color: #4CAF50;
        }

        .sidebar-menu i {
            margin-right: 12px;
            width: 20px;
        }

        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 30px;
        }

        .content-header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .content-header h1 {
            color: #083464;
            font-size: 2rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .dashboard-tabs {
            display: flex;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 20px;
            background: transparent;
            border: none;
            color: #666;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .tab-btn:hover {
            background: #f8f9fa;
            color: #083464;
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #083464, #1976D2);
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 20px;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.pending { background: linear-gradient(135deg, #FF9800, #F57C00); }
        .stat-icon.approved { background: linear-gradient(135deg, #4CAF50, #388E3C); }
        .stat-icon.rejected { background: linear-gradient(135deg, #F44336, #D32F2F); }
        .stat-icon.total { background: linear-gradient(135deg, #2196F3, #1976D2); }

        .stat-info h3 {
            color: #083464;
            font-size: 1rem;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin: 0;
        }

        .data-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table-header {
            background: linear-gradient(135deg, #083464, #1976D2);
            color: white;
            padding: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-header h2 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .enhanced-table {
            width: 100%;
            border-collapse: collapse;
        }

        .enhanced-table th,
        .enhanced-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .enhanced-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #083464;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .enhanced-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-badge.pending {
            background: rgba(255, 152, 0, 0.1);
            color: #F57C00;
        }

        .action-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 3px;
        }

        .btn-approve {
            background: linear-gradient(135deg, #4CAF50, #388E3C);
            color: white;
        }

        .btn-reject {
            background: linear-gradient(135deg, #F44336, #D32F2F);
            color: white;
        }

        .btn-approve:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .btn-reject:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
        }

        .proof-link {
            color: #1976D2;
            text-decoration: none;
            font-weight: 600;
        }

        .proof-link:hover {
            text-decoration: underline;
        }

        .amount-cell {
            font-weight: 700;
            color: #4CAF50;
        }

        .no-data {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .no-data i {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .dashboard-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Finance Profile" class="profile-image">
                <h2>{{ session.name }}</h2>
                <p>Finance Officer</p>
                <p style="font-size: 0.8rem; opacity: 0.8;">ID: {{ session.user_id }}</p>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="{{ url_for('finance_dashboard') }}" class="active">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manage_fees') }}">
                            <i class="fas fa-money-bill-wave"></i> Manage Fees
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}" style="margin-bottom: 20px; padding: 15px; border-radius: 10px; background: {% if category == 'success' %}#d4edda{% elif category == 'error' %}#f8d7da{% else %}#fff3cd{% endif %}; border: 1px solid {% if category == 'success' %}#c3e6cb{% elif category == 'error' %}#f5c6cb{% else %}#ffeaa7{% endif %}; color: {% if category == 'success' %}#155724{% elif category == 'error' %}#721c24{% else %}#856404{% endif %};">
                            <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% else %}exclamation-triangle{% endif %}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Header -->
            <div class="content-header">
                <h1><i class="fas fa-chart-line"></i> Finance Dashboard</h1>
                <p class="subtitle">Manage transcript requests and monitor payment verification</p>
            </div>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Pending Requests</h3>
                        <p class="stat-number">{{ pending_count or 0 }}</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon approved">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Approved Today</h3>
                        <p class="stat-number">{{ approved_count or 0 }}</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon rejected">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Rejected Today</h3>
                        <p class="stat-number">{{ rejected_count or 0 }}</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon total">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Total Requests</h3>
                        <p class="stat-number">{{ total_count or 0 }}</p>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <div class="dashboard-tabs">
                <button class="tab-btn active" onclick="showTab('view-status')">
                    <i class="fas fa-eye"></i> View Status
                </button>
                <button class="tab-btn" onclick="showTab('view-history')">
                    <i class="fas fa-history"></i> View History
                </button>
            </div>

            <!-- View Status Tab -->
            <div id="view-status" class="tab-content active">
                <div class="data-table">
                    <div class="table-header">
                        <h2><i class="fas fa-list-alt"></i> Pending Requests</h2>
                        <span>{{ pending_requests|length }} pending request(s)</span>
                    </div>
                    <div class="table-responsive">
                        {% if pending_requests %}
                        <table class="enhanced-table">
                            <thead>
                                <tr>
                                    <th>Request ID</th>
                                    <th>Student Name</th>
                                    <th>Date</th>
                                    <th>Academic Years</th>
                                    <th>Amount</th>
                                    <th>Payment Method</th>
                                    <th>Proof</th>
                                    <th>School Fees Paid</th>
                                    <th>Total Fees Required</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in pending_requests %}
                                <tr>
                                    <td><strong>{{ request.request_number or request.id }}</strong></td>
                                    <td>{{ request.student_name }}</td>
                                    <td>{{ request.date.strftime('%Y-%m-%d') if request.date else 'N/A' }}</td>
                                    <td>{{ request.academic_years|join(', ') if request.academic_years else 'N/A' }}</td>
                                    <td class="amount-cell">{{ "{:,.0f}".format(request.total_amount) }} RWF</td>
                                    <td>{{ request.payment_method|title }}</td>
                                    <td>
                                        {% if request.payment_proof_filename %}
                                        <a href="{{ url_for('view_payment_proof', filename=request.payment_proof_filename) }}"
                                           class="proof-link" target="_blank">
                                            <i class="fas fa-image"></i> View Proof
                                        </a>
                                        {% else %}
                                        <span style="color: #999;">No proof</span>
                                        {% endif %}
                                    </td>
                                    <td class="amount-cell">{{ "{:,.0f}".format(request.school_fees_paid or 0) }} RWF</td>
                                    <td class="amount-cell">{{ "{:,.0f}".format(request.total_fees_required or 0) }} RWF</td>
                                    <td>
                                        <span class="status-badge pending">
                                            <img src="{{ url_for('static', filename='images/13.png') }}" alt="Pending" style="width: 16px; height: 16px;">
                                            Pending
                                        </span>
                                    </td>
                                    <td>
                                        <form method="POST" action="{{ url_for('finance_approve_request') }}" style="display: inline;">
                                            <input type="hidden" name="request_id" value="{{ request.id }}">
                                            <button type="submit" class="action-btn btn-approve"
                                                    onclick="return confirm('Are you sure you want to approve this request?')">
                                                <i class="fas fa-check"></i> Approve
                                            </button>
                                        </form>
                                        <form method="POST" action="{{ url_for('finance_reject_request') }}" style="display: inline;">
                                            <input type="hidden" name="request_id" value="{{ request.id }}">
                                            <button type="submit" class="action-btn btn-reject"
                                                    onclick="return confirm('Are you sure you want to reject this request?')">
                                                <i class="fas fa-times"></i> Reject
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                        {% else %}
                        <div class="no-data">
                            <i class="fas fa-inbox"></i>
                            <h3>No Pending Requests</h3>
                            <p>All transcript requests have been processed.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- View History Tab -->
            <div id="view-history" class="tab-content">
                <!-- Approved Requests Section -->
                <div class="data-table" style="margin-bottom: 30px;">
                    <div class="table-header">
                        <h2><i class="fas fa-check-circle"></i> Approved Requests</h2>
                        <span>{{ approved_requests|length }} approved request(s)</span>
                    </div>
                    <div class="table-responsive">
                        {% if approved_requests %}
                        <table class="enhanced-table">
                            <thead>
                                <tr>
                                    <th>Request ID</th>
                                    <th>Student Name</th>
                                    <th>Date Requested</th>
                                    <th>Date Approved</th>
                                    <th>Academic Years</th>
                                    <th>Amount</th>
                                    <th>Payment Method</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in approved_requests %}
                                <tr>
                                    <td><strong>{{ request.request_number or request.id }}</strong></td>
                                    <td>{{ request.student_name }}</td>
                                    <td>{{ request.date.strftime('%Y-%m-%d') if request.date else 'N/A' }}</td>
                                    <td>{{ request.approved_date.strftime('%Y-%m-%d') if request.approved_date else 'N/A' }}</td>
                                    <td>{{ request.academic_years|join(', ') if request.academic_years else 'N/A' }}</td>
                                    <td class="amount-cell">{{ "{:,.0f}".format(request.total_amount) }} RWF</td>
                                    <td>{{ request.payment_method|title }}</td>
                                    <td>
                                        <span class="status-badge" style="background: rgba(76, 175, 80, 0.1); color: #388E3C;">
                                            <img src="{{ url_for('static', filename='images/approved.png') }}" alt="Approved" style="width: 16px; height: 16px;">
                                            {{ request.status|title }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                        {% else %}
                        <div class="no-data">
                            <i class="fas fa-check-circle"></i>
                            <h3>No Approved Requests</h3>
                            <p>No requests have been approved yet.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Rejected Requests Section -->
                <div class="data-table">
                    <div class="table-header">
                        <h2><i class="fas fa-times-circle"></i> Rejected Requests</h2>
                        <span>{{ rejected_requests|length }} rejected request(s)</span>
                    </div>
                    <div class="table-responsive">
                        {% if rejected_requests %}
                        <table class="enhanced-table">
                            <thead>
                                <tr>
                                    <th>Request ID</th>
                                    <th>Student Name</th>
                                    <th>Date Requested</th>
                                    <th>Date Rejected</th>
                                    <th>Academic Years</th>
                                    <th>Amount</th>
                                    <th>Payment Method</th>
                                    <th>Reason</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in rejected_requests %}
                                <tr>
                                    <td><strong>{{ request.request_number or request.id }}</strong></td>
                                    <td>{{ request.student_name }}</td>
                                    <td>{{ request.date.strftime('%Y-%m-%d') if request.date else 'N/A' }}</td>
                                    <td>{{ request.rejected_at.strftime('%Y-%m-%d') if request.rejected_at else 'N/A' }}</td>
                                    <td>{{ request.academic_years|join(', ') if request.academic_years else 'N/A' }}</td>
                                    <td class="amount-cell">{{ "{:,.0f}".format(request.total_amount) }} RWF</td>
                                    <td>{{ request.payment_method|title }}</td>
                                    <td>{{ request.rejection_reason or 'No reason provided' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                        {% else %}
                        <div class="no-data">
                            <i class="fas fa-times-circle"></i>
                            <h3>No Rejected Requests</h3>
                            <p>No requests have been rejected.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-btn');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Auto-hide flash messages
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        alert.style.display = 'none';
                    }, 300);
                }, 5000);
            });
        });
    </script>
</body>
</html>
