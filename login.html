<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - IN<PERSON> Ruhengeri</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        /* Top Navigation */
        .top-nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(76, 175, 80, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-brand {
            display: flex;
            align-items: center;
            color: white;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.2rem;
            gap: 10px;
        }

        .nav-brand img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 30px;
            font-weight: 500;
            transition: opacity 0.3s ease;
            padding: 8px 16px;
            border-radius: 20px;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }

        /* Login Container */
        .login-container {
            position: relative;
            height: 100vh;
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('static/images/login_image.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            display: flex;
            align-items: center;
            justify-content: center;
            padding-top: 80px;
        }

        .login-form-wrapper {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 50px 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            border: 1px solid rgba(255,255,255,0.3);
            max-width: 450px;
            width: 100%;
            margin: 20px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-header img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 20px;
            border: 3px solid #4CAF50;
        }

        .login-header h2 {
            color: #333;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 1rem;
        }

        /* Role Selection */
        .role-selection {
            margin-bottom: 30px;
        }

        .role-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 5px;
            margin-bottom: 20px;
        }

        .role-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #666;
            font-size: 14px;
        }

        .role-tab.active {
            background: #4CAF50;
            color: white;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.9);
        }

        .form-control:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 18px;
        }

        .input-group .form-control {
            padding-left: 45px;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4CAF50, #388E3C);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        /* Department/Faculty Selection */
        .department-field {
            display: none;
        }

        .department-field.show {
            display: block;
        }

        /* Error Messages */
        .error-message {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #d32f2f;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        /* Footer */
        .login-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }

        .login-footer p {
            color: #666;
            font-size: 14px;
            margin: 0;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .login-form-wrapper {
                margin: 10px;
                padding: 30px 25px;
            }

            .login-header h2 {
                font-size: 1.5rem;
            }

            .nav-links a {
                margin-left: 15px;
            }

            .nav-container {
                flex-direction: column;
                gap: 10px;
                padding: 15px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navigation -->
    <div class="top-nav">
        <div class="nav-container">
            <a href="homepage.html" class="nav-brand">
                <img src="static/images/ines-logo.png" alt="INES Logo" 
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM0Q0FGNTASCZ8L2NpcmNsZT4KPHRleHQgeD0iNTAlIiB5PSI1NSUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJjZW50cmFsIj5JTkVTPC90ZXh0Pgo8L3N2Zz4K'">
                INES Ruhengeri
            </a>
            <div class="nav-links">
                <a href="homepage.html">🏠 Home</a>
                <a href="login.html">🔐 Login</a>
            </div>
        </div>
    </div>

    <!-- Login Container -->
    <div class="login-container">
        <div class="login-form-wrapper">
            <!-- Login Header -->
            <div class="login-header">
                <img src="static/images/ines-logo.png" alt="INES Logo"
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDAiIGN5PSI0MCIgcj0iNDAiIGZpbGw9IiM0Q0FGNTASCZ8L2NpcmNsZT4KPHRleHQgeD0iNTAlIiB5PSI1NSUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJjZW50cmFsIj5JTkVTPC90ZXh0Pgo8L3N2Zz4K'">
                <h2>Welcome Back</h2>
                <p>Sign in to access your portal</p>
            </div>

            <!-- Error Message -->
            <div class="error-message" id="errorMessage" style="display: none;">
                <span id="errorText"></span>
            </div>

            <!-- Login Form -->
            <form id="loginForm">
                <!-- Role Selection -->
                <div class="role-selection">
                    <div class="role-tabs">
                        <div class="role-tab active" data-role="student">
                            🎓 Student
                        </div>
                        <div class="role-tab" data-role="faculty">
                            👨‍🏫 Faculty
                        </div>
                        <div class="role-tab" data-role="finance">
                            💰 Finance
                        </div>
                    </div>
                </div>

                <!-- Email Field -->
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <div class="input-group">
                        <span class="input-icon">📧</span>
                        <input type="email" class="form-control" id="email" name="email" required 
                               placeholder="Enter your email address">
                    </div>
                </div>

                <!-- Password Field -->
                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-group">
                        <span class="input-icon">🔒</span>
                        <input type="password" class="form-control" id="password" name="password" required 
                               placeholder="Enter your password">
                    </div>
                </div>

                <!-- Department Field (for students) -->
                <div class="form-group department-field" id="departmentField">
                    <label for="department">Department</label>
                    <div class="input-group">
                        <span class="input-icon">🏢</span>
                        <select class="form-control" id="department" name="department">
                            <option value="">Select your department</option>
                            <option value="computer_science">Computer Science</option>
                            <option value="business_administration">Business Administration</option>
                            <option value="engineering">Engineering</option>
                            <option value="education">Education</option>
                            <option value="agriculture">Agriculture</option>
                        </select>
                    </div>
                </div>

                <!-- Faculty Field (for faculty) -->
                <div class="form-group department-field" id="facultyField">
                    <label for="faculty">Faculty</label>
                    <div class="input-group">
                        <span class="input-icon">🏛️</span>
                        <select class="form-control" id="faculty" name="faculty">
                            <option value="">Select your faculty</option>
                            <option value="science_technology">Science & Technology</option>
                            <option value="business_economics">Business & Economics</option>
                            <option value="education">Education</option>
                            <option value="agriculture">Agriculture</option>
                        </select>
                    </div>
                </div>

                <!-- Login Button -->
                <button type="submit" class="login-btn">
                    🔐 Sign In
                </button>
            </form>

            <!-- Footer -->
            <div class="login-footer">
                <p>&copy; 2024 INES Ruhengeri. All rights reserved.</p>
            </div>
        </div>
    </div>

    <script>
        // Role tab functionality
        const roleTabs = document.querySelectorAll('.role-tab');
        const departmentField = document.getElementById('departmentField');
        const facultyField = document.getElementById('facultyField');
        const loginForm = document.getElementById('loginForm');
        let selectedRole = 'student';

        roleTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                roleTabs.forEach(t => t.classList.remove('active'));
                
                // Add active class to clicked tab
                this.classList.add('active');
                
                // Get selected role
                selectedRole = this.dataset.role;
                
                // Show/hide department/faculty fields
                departmentField.classList.remove('show');
                facultyField.classList.remove('show');
                
                if (selectedRole === 'student') {
                    departmentField.classList.add('show');
                } else if (selectedRole === 'faculty') {
                    facultyField.classList.add('show');
                }
            });
        });

        // Initialize with student role
        document.addEventListener('DOMContentLoaded', function() {
            departmentField.classList.add('show');
        });

        // Form submission
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const department = document.getElementById('department').value;
            const faculty = document.getElementById('faculty').value;
            
            // Basic validation
            if (!email || !password) {
                showError('Please fill in all required fields.');
                return;
            }
            
            if (selectedRole === 'student' && !department) {
                showError('Please select your department.');
                return;
            }
            
            if (selectedRole === 'faculty' && !faculty) {
                showError('Please select your faculty.');
                return;
            }
            
            // Simulate login process
            simulateLogin(email, password, selectedRole, department || faculty);
        });

        function simulateLogin(email, password, role, department) {
            // Show loading state
            const loginBtn = document.querySelector('.login-btn');
            const originalText = loginBtn.innerHTML;
            loginBtn.innerHTML = '⏳ Signing in...';
            loginBtn.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                // Demo credentials for testing
                const demoCredentials = {
                    '<EMAIL>': { password: 'student123', role: 'student' },
                    '<EMAIL>': { password: 'faculty123', role: 'faculty' },
                    '<EMAIL>': { password: 'finance123', role: 'finance' }
                };
                
                const user = demoCredentials[email];
                
                if (user && user.password === password && user.role === role) {
                    // Success
                    loginBtn.innerHTML = '✅ Success!';
                    setTimeout(() => {
                        alert(`Welcome! You would be redirected to the ${role} dashboard.`);
                        // In a real app, redirect to the appropriate dashboard
                        window.location.href = 'homepage.html';
                    }, 1000);
                } else {
                    // Error
                    showError('Invalid credentials. Try: <EMAIL> / student123');
                    loginBtn.innerHTML = originalText;
                    loginBtn.disabled = false;
                }
            }, 2000);
        }

        function showError(message) {
            const errorMessage = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            errorText.textContent = message;
            errorMessage.style.display = 'block';
            
            // Hide error after 5 seconds
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }

        // Get role from URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const roleParam = urlParams.get('role');
        if (roleParam) {
            const roleTab = document.querySelector(`[data-role="${roleParam}"]`);
            if (roleTab) {
                roleTab.click();
            }
        }
    </script>
</body>
</html>
