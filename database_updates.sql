-- Database updates for enhanced finance system
-- Run these SQL commands to update your database schema

-- Add missing columns to new_transcript_requests table
ALTER TABLE new_transcript_requests
ADD COLUMN payment_proof_filename VARCHAR(255) NULL;

ALTER TABLE new_transcript_requests
ADD COLUMN approved_date DATETIME NULL;

ALTER TABLE new_transcript_requests
ADD COLUMN rejected_at DATETIME NULL;

ALTER TABLE new_transcript_requests
ADD COLUMN rejection_reason TEXT NULL;

ALTER TABLE new_transcript_requests
ADD COLUMN finance_approved_by VARCHAR(50) NULL;

ALTER TABLE new_transcript_requests
ADD COLUMN finance_rejected_by VARCHAR(50) NULL;

-- Create school_fees_payments table if it doesn't exist
CREATE TABLE IF NOT EXISTS school_fees_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    academic_year VARCHAR(20) NOT NULL,
    department VARCHAR(100) NOT NULL,
    total_fees_required DECIMAL(10,2) NOT NULL DEFAULT 0,
    amount_paid DECIMAL(10,2) NOT NULL DEFAULT 0,
    payment_status ENUM('unpaid', 'partial', 'paid') DEFAULT 'unpaid',
    last_payment_date DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_year (student_id, academic_year)
);

-- Insert sample school fees data for testing
INSERT INTO school_fees_payments (student_id, academic_year, department, total_fees_required, amount_paid, payment_status) VALUES
-- Computer Science students
(1, '2023-2024', 'Computer Science', 750000.00, 750000.00, 'paid'),
(1, '2022-2023', 'Computer Science', 700000.00, 700000.00, 'paid'),
(1, '2021-2022', 'Computer Science', 650000.00, 650000.00, 'paid'),

(2, '2023-2024', 'Computer Science', 750000.00, 500000.00, 'partial'),
(2, '2022-2023', 'Computer Science', 700000.00, 700000.00, 'paid'),

(3, '2023-2024', 'Computer Science', 750000.00, 750000.00, 'paid'),
(3, '2022-2023', 'Computer Science', 700000.00, 700000.00, 'paid'),

-- Civil Engineering students
(4, '2023-2024', 'Civil Engineering', 800000.00, 800000.00, 'paid'),
(4, '2022-2023', 'Civil Engineering', 750000.00, 750000.00, 'paid'),

(5, '2023-2024', 'Civil Engineering', 800000.00, 600000.00, 'partial'),

-- Business students
(6, '2023-2024', 'Cooperatives Management', 650000.00, 650000.00, 'paid'),
(6, '2022-2023', 'Cooperatives Management', 600000.00, 600000.00, 'paid'),

(7, '2023-2024', 'Cooperatives Management', 650000.00, 0.00, 'unpaid'),

-- Law students
(8, '2023-2024', 'Law', 700000.00, 700000.00, 'paid'),
(8, '2022-2023', 'Law', 650000.00, 650000.00, 'paid'),

(9, '2023-2024', 'Law', 700000.00, 400000.00, 'partial'),

-- Architecture students
(10, '2023-2024', 'Architecture', 850000.00, 850000.00, 'paid'),
(10, '2022-2023', 'Architecture', 800000.00, 800000.00, 'paid');

-- Create upload directories (run this in your application)
-- mkdir -p static/uploads/payment_proofs

-- Update existing requests to have proper status
UPDATE new_transcript_requests 
SET status = 'pending_finance' 
WHERE status IN ('pending', 'pending_confirmation');

-- Add indexes for better performance
CREATE INDEX idx_transcript_requests_status ON new_transcript_requests(status);
CREATE INDEX idx_transcript_requests_student_id ON new_transcript_requests(student_id);
CREATE INDEX idx_school_fees_student_year ON school_fees_payments(student_id, academic_year);
CREATE INDEX idx_payment_proof ON new_transcript_requests(payment_proof_filename);

-- Sample data for testing the new system
-- Insert some test transcript requests with payment proofs

-- Test request 1: Pending with payment proof
INSERT INTO new_transcript_requests (
    student_id, request_number, academic_years, total_transcripts, total_amount,
    payment_method, payment_proof_filename, status, purpose, institution_name, created_at
) VALUES (
    1, 'REQ-2025-001', '["2023-2024"]', 1, 1000.00,
    'mobile_money', 'payment_proof_STU001_20250116_120000_momo_receipt.jpg', 'pending_finance', 
    'Job Application', 'Private Company Ltd', NOW()
);

-- Test request 2: Pending with bank transfer proof
INSERT INTO new_transcript_requests (
    student_id, request_number, academic_years, total_transcripts, total_amount,
    payment_method, payment_proof_filename, status, purpose, institution_name, created_at
) VALUES (
    2, 'REQ-2025-002', '["2022-2023", "2023-2024"]', 2, 2000.00,
    'bank_transfer', 'payment_proof_STU002_20250116_130000_bank_receipt.pdf', 'pending_finance', 
    'Further Studies', 'University of Rwanda', NOW()
);

-- Test request 3: Pending cash payment
INSERT INTO new_transcript_requests (
    student_id, request_number, academic_years, total_transcripts, total_amount,
    payment_method, payment_proof_filename, status, purpose, institution_name, created_at
) VALUES (
    3, 'REQ-2025-003', '["2023-2024"]', 1, 1000.00,
    'cash', 'payment_proof_STU003_20250116_140000_cash_receipt.jpg', 'pending_finance', 
    'Scholarship Application', 'Mastercard Foundation', NOW()
);

-- Verify the updates
SELECT 'Database updates completed successfully!' as status;

-- Check the new structure
DESCRIBE new_transcript_requests;
DESCRIBE school_fees_payments;
