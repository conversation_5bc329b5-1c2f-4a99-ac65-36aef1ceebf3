/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #4CAF50;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #388E3C;
}

/* Header Styles */
.top-header {
    background: linear-gradient(135deg, #8BC34A, #689F38);
    padding: 8px 0;
    font-size: 14px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.top-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.top-header .left-links a,
.top-header .right-links a {
    color: white;
    text-decoration: none;
    margin-right: 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 5px 10px;
    border-radius: 15px;
}

.top-header .left-links a:hover,
.top-header .right-links a:hover,
.top-header .left-links a.active {
    background: rgba(255,255,255,0.2);
    transform: translateY(-1px);
}

/* Main Header */
.main-header {
    background: linear-gradient(135deg, #4CAF50, #388E3C);
    padding: 15px 0;
    margin-top: 40px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo-section {
    display: flex;
    align-items: center;
}

.logo-section img {
    width: 50px;
    height: 50px;
    margin-right: 15px;
    border-radius: 50%;
    border: 2px solid white;
    transition: transform 0.3s ease;
}

.logo-section img:hover {
    transform: scale(1.1);
}

.logo-text {
    color: white;
}

.logo-text h1 {
    font-size: 28px;
    font-weight: bold;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.logo-text p {
    font-size: 14px;
    margin: 0;
    opacity: 0.9;
}

.header-info {
    text-align: right;
    color: white;
}

.header-info h3 {
    font-size: 18px;
    margin: 0;
    font-weight: 600;
}

.header-info p {
    font-size: 14px;
    margin: 0;
    opacity: 0.9;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('static/images/home_image.JPG');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.1), rgba(56, 142, 60, 0.1));
    animation: gradientShift 10s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { opacity: 0.1; }
    50% { opacity: 0.3; }
}

.hero-content {
    max-width: 800px;
    padding: 60px 40px;
    background: rgba(255,255,255,0.1);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255,255,255,0.2);
    position: relative;
    z-index: 2;
    animation: slideUp 1s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-content p {
    font-size: 1.3rem;
    margin-bottom: 30px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-btn {
    padding: 15px 30px;
    font-size: 18px;
    font-weight: 600;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.hero-btn.primary {
    background: linear-gradient(135deg, #4CAF50, #388E3C);
    color: white;
}

.hero-btn.secondary {
    background: rgba(255,255,255,0.9);
    color: #333;
}

.hero-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 1s ease-out;
}

.fade-in-delay {
    animation: fadeIn 1s ease-out 0.3s both;
}

.fade-in-delay-2 {
    animation: fadeIn 1s ease-out 0.6s both;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Features Section */
.features-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    position: relative;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

.section-title {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto 50px;
    text-align: center;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4CAF50, #388E3C);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.feature-card h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.feature-btn {
    background: linear-gradient(135deg, #4CAF50, #388E3C);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.feature-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

/* Statistics Section */
.stats-section {
    background: linear-gradient(135deg, #4CAF50, #388E3C);
    padding: 80px 0;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    text-align: center;
}

.stat-item {
    padding: 20px;
}

.stat-number {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Footer */
.footer-section {
    background: linear-gradient(rgba(0,0,0,0.8), rgba(0,0,0,0.8)), url('static/images/footer.png');
    background-size: cover;
    background-position: center;
    padding: 60px 0 20px;
    color: white;
    position: relative;
}

.footer-content {
    position: relative;
    z-index: 1;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-column h4 {
    color: #4CAF50;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.footer-column p,
.footer-column li {
    margin-bottom: 10px;
    opacity: 0.9;
    line-height: 1.6;
}

.footer-column ul {
    list-style: none;
    padding: 0;
}

.footer-column a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-column a:hover {
    color: #4CAF50;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #4CAF50;
    transform: translateY(-3px);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.contact-item i {
    color: #4CAF50;
    width: 20px;
}

.support-btn {
    background: linear-gradient(135deg, #4CAF50, #388E3C);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 15px;
}

.support-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.2);
    padding-top: 20px;
    text-align: center;
    opacity: 0.8;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .top-header .container {
        flex-direction: column;
        gap: 10px;
        padding: 10px 20px;
    }

    .main-header .container {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .hero-section {
        background-attachment: scroll;
    }
}

@media (max-width: 480px) {
    .hero-content {
        padding: 40px 20px;
        margin: 20px;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-number {
        font-size: 2.5rem;
    }
}
