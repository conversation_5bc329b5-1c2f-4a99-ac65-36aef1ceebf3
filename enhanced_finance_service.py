"""
Enhanced Finance Service - Automatic School Fees Validation
Implements automatic approval/rejection based on school fees payment status
"""

import pymysql
import json
from datetime import datetime
from new_database_service import get_db_connection

# School fees structure per academic year and department
SCHOOL_FEES_STRUCTURE = {
    # Faculty of Sciences and Information Technology
    'Computer Science': {
        '2020-2021': 750000, '2021-2022': 750000, '2022-2023': 750000, '2023-2024': 750000, '2024-2025': 750000
    },
    'Statistics Applied to Economy': {
        '2020-2021': 750000, '2021-2022': 750000, '2022-2023': 750000, '2023-2024': 750000, '2024-2025': 750000
    },
    
    # Faculty of Economics Social Sciences and Management
    'Cooperatives Management': {
        '2020-2021': 750000, '2021-2022': 750000, '2022-2023': 750000, '2023-2024': 750000, '2024-2025': 750000
    },
    'Entrepreneurship and SME\'s Management': {
        '2020-2021': 750000, '2021-2022': 750000, '2022-2023': 750000, '2023-2024': 750000, '2024-2025': 750000
    },
    
    # Faculty of Education
    'French and English': {
        '2020-2021': 650000, '2021-2022': 650000, '2022-2023': 650000, '2023-2024': 650000, '2024-2025': 650000
    },
    
    # Faculty of Engineering and Technology
    'Civil Engineering': {
        '2020-2021': 800000, '2021-2022': 800000, '2022-2023': 800000, '2023-2024': 800000, '2024-2025': 800000
    },
    'Biotechnologies': {
        '2020-2021': 800000, '2021-2022': 800000, '2022-2023': 800000, '2023-2024': 800000, '2024-2025': 800000
    },
    'Land Survey': {
        '2020-2021': 800000, '2021-2022': 800000, '2022-2023': 800000, '2023-2024': 800000, '2024-2025': 800000
    },
    'Architecture': {
        '2020-2021': 800000, '2021-2022': 800000, '2022-2023': 800000, '2023-2024': 800000, '2024-2025': 800000
    },
    'Water Engineering': {
        '2020-2021': 800000, '2021-2022': 800000, '2022-2023': 800000, '2023-2024': 800000, '2024-2025': 800000
    }
}

def create_school_fees_table():
    """Create school fees payment tracking table"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS school_fees_payments (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        student_id INT NOT NULL,
                        academic_year VARCHAR(20) NOT NULL,
                        department VARCHAR(255) NOT NULL,
                        total_fees_required DECIMAL(10,2) NOT NULL,
                        amount_paid DECIMAL(10,2) NOT NULL DEFAULT 0,
                        payment_status ENUM('unpaid', 'partial', 'paid') DEFAULT 'unpaid',
                        last_payment_date TIMESTAMP NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        
                        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_student_year (student_id, academic_year),
                        INDEX idx_student_year (student_id, academic_year),
                        INDEX idx_payment_status (payment_status)
                    )
                """)
                connection.commit()
                print("✅ School fees payments table created")
                return True
    except Exception as e:
        print(f"❌ Error creating school fees table: {e}")
        return False

def initialize_student_fees(student_id, department, enrollment_year, program_duration):
    """Initialize school fees records for a student"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # Generate academic years for the student
                academic_years = []
                for i in range(program_duration):
                    year_start = enrollment_year + i
                    year_end = year_start + 1
                    academic_year = f"{year_start}-{year_end}"
                    academic_years.append(academic_year)
                
                # Insert fee records for each academic year
                for academic_year in academic_years:
                    required_fees = SCHOOL_FEES_STRUCTURE.get(department, {}).get(academic_year, 750000)
                    
                    cursor.execute("""
                        INSERT IGNORE INTO school_fees_payments 
                        (student_id, academic_year, department, total_fees_required, amount_paid, payment_status)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """, (student_id, academic_year, department, required_fees, 0, 'unpaid'))
                
                connection.commit()
                print(f"✅ Initialized fees for student {student_id} - {len(academic_years)} years")
                return True
    except Exception as e:
        print(f"❌ Error initializing student fees: {e}")
        return False

def record_fee_payment(student_id, academic_year, amount_paid):
    """Record a school fee payment"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # Update the payment amount
                cursor.execute("""
                    UPDATE school_fees_payments 
                    SET amount_paid = amount_paid + %s,
                        last_payment_date = %s
                    WHERE student_id = %s AND academic_year = %s
                """, (amount_paid, datetime.now(), student_id, academic_year))
                
                # Update payment status based on amount paid vs required
                cursor.execute("""
                    UPDATE school_fees_payments 
                    SET payment_status = CASE 
                        WHEN amount_paid >= total_fees_required THEN 'paid'
                        WHEN amount_paid > 0 THEN 'partial'
                        ELSE 'unpaid'
                    END
                    WHERE student_id = %s AND academic_year = %s
                """, (student_id, academic_year))
                
                connection.commit()
                return True
    except Exception as e:
        print(f"❌ Error recording fee payment: {e}")
        return False

def check_school_fees_status(student_id, academic_years):
    """
    Check if school fees are fully paid for requested academic years
    Returns: (is_fully_paid, unpaid_details)
    """
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                unpaid_details = []
                total_required = 0
                total_paid = 0
                
                for academic_year in academic_years:
                    cursor.execute("""
                        SELECT academic_year, department, total_fees_required, amount_paid, payment_status
                        FROM school_fees_payments 
                        WHERE student_id = %s AND academic_year = %s
                    """, (student_id, academic_year))
                    
                    fee_record = cursor.fetchone()
                    
                    if not fee_record:
                        # If no record exists, create one with default values
                        cursor.execute("""
                            SELECT d.name as department FROM students s 
                            JOIN departments d ON s.department_id = d.id 
                            WHERE s.id = %s
                        """, (student_id,))
                        dept_result = cursor.fetchone()
                        department = dept_result['department'] if dept_result else 'Unknown'
                        
                        required_fees = SCHOOL_FEES_STRUCTURE.get(department, {}).get(academic_year, 750000)
                        
                        unpaid_details.append({
                            'academic_year': academic_year,
                            'department': department,
                            'required': required_fees,
                            'paid': 0,
                            'balance': required_fees,
                            'status': 'unpaid'
                        })
                        total_required += required_fees
                    else:
                        required = float(fee_record['total_fees_required'])
                        paid = float(fee_record['amount_paid'])
                        balance = required - paid
                        
                        total_required += required
                        total_paid += paid
                        
                        if fee_record['payment_status'] != 'paid':
                            unpaid_details.append({
                                'academic_year': academic_year,
                                'department': fee_record['department'],
                                'required': required,
                                'paid': paid,
                                'balance': balance,
                                'status': fee_record['payment_status']
                            })
                
                is_fully_paid = len(unpaid_details) == 0
                
                return is_fully_paid, {
                    'unpaid_years': unpaid_details,
                    'total_required': total_required,
                    'total_paid': total_paid,
                    'total_balance': total_required - total_paid
                }
                
    except Exception as e:
        print(f"❌ Error checking school fees status: {e}")
        return False, {'error': str(e)}

def auto_process_transcript_request(request_id):
    """
    Automatically process transcript request based on school fees status
    Returns: (status, reason)
    """
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Get request details
                cursor.execute("""
                    SELECT tr.*, s.id as student_id, u.name as student_name, u.email,
                           d.name as department
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    JOIN departments d ON s.department_id = d.id
                    WHERE tr.id = %s
                """, (request_id,))
                
                request_data = cursor.fetchone()
                if not request_data:
                    return 'error', 'Request not found'
                
                academic_years = json.loads(request_data['academic_years'])
                student_id = request_data['student_id']
                
                # Check school fees status
                is_fully_paid, fee_details = check_school_fees_status(student_id, academic_years)
                
                if is_fully_paid:
                    # Auto-approve but keep in pending status until finance confirms
                    cursor.execute("""
                        UPDATE new_transcript_requests
                        SET status = 'pending_confirmation',
                            auto_decision = 'approve',
                            auto_decision_reason = %s,
                            auto_processed_at = %s
                        WHERE id = %s
                    """, ('School fees fully paid - Auto-approved, awaiting finance confirmation', datetime.now(), request_id))

                    connection.commit()
                    return 'pending_confirmation', 'School fees verified - Request auto-approved, awaiting finance confirmation'
                else:
                    # Auto-reject but keep in pending status until finance confirms
                    unpaid_summary = []
                    for unpaid in fee_details['unpaid_years']:
                        unpaid_summary.append(f"{unpaid['academic_year']}: {unpaid['balance']:,.0f} RWF balance")

                    rejection_reason = f"School fees not fully paid. Unpaid amounts: {'; '.join(unpaid_summary)}"

                    cursor.execute("""
                        UPDATE new_transcript_requests
                        SET status = 'pending_confirmation',
                            auto_decision = 'reject',
                            auto_decision_reason = %s,
                            auto_processed_at = %s,
                            rejection_reason = %s
                        WHERE id = %s
                    """, (rejection_reason, datetime.now(), rejection_reason, request_id))

                    connection.commit()
                    return 'pending_confirmation', f'School fees verification complete - Request auto-rejected, awaiting finance confirmation: {rejection_reason}'
                
    except Exception as e:
        print(f"❌ Error auto-processing request: {e}")
        return 'error', str(e)

def get_pending_confirmations():
    """
    Get all requests pending finance confirmation
    Returns: List of requests awaiting confirmation
    """
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT tr.*, u.name as student_name, u.reg_no as student_id, u.email,
                           d.name as department, f.name as faculty
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    JOIN departments d ON s.department_id = d.id
                    JOIN faculties f ON d.faculty_id = f.id
                    WHERE tr.status = 'pending_confirmation'
                    ORDER BY tr.auto_processed_at ASC
                """)

                requests = cursor.fetchall()

                # Convert to format for display
                result = []
                for req in requests:
                    result.append({
                        'id': str(req['id']),
                        'student_id': req['student_id'],
                        'student_name': req['student_name'],
                        'email': req['email'],
                        'department': req['department'],
                        'faculty': req['faculty'],
                        'academic_years': json.loads(req['academic_years']),
                        'count': req['total_transcripts'],
                        'total_price': float(req['total_amount']),
                        'payment_method': req['payment_method'],
                        'status': req['status'],
                        'auto_decision': req['auto_decision'],
                        'auto_decision_reason': req['auto_decision_reason'],
                        'date': req['created_at'].strftime('%Y-%m-%d'),
                        'auto_processed_at': req['auto_processed_at'].strftime('%Y-%m-%d %H:%M:%S') if req['auto_processed_at'] else None
                    })

                return result

    except Exception as e:
        print(f"❌ Error getting pending confirmations: {e}")
        return []

def get_finance_history():
    """Get all processed requests (approved, rejected, and pending confirmation) for finance history view"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT tr.*, u.name as student_name, u.reg_no as student_id, u.email,
                           d.name as department, f.name as faculty
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    JOIN departments d ON s.department_id = d.id
                    JOIN faculties f ON d.faculty_id = f.id
                    WHERE tr.status IN ('approved_finance', 'rejected', 'completed', 'pending_confirmation')
                    ORDER BY tr.created_at DESC
                """)

                requests = cursor.fetchall()

                # Convert to the format expected by templates
                result = []
                for req in requests:
                    result.append({
                        'id': str(req['id']),
                        'student_id': req['student_id'],
                        'student_name': req['student_name'],
                        'email': req['email'],
                        'department': req['department'],
                        'faculty': req['faculty'],
                        'academic_years': json.loads(req['academic_years']),
                        'count': req['total_transcripts'],
                        'total_price': float(req['total_amount']),
                        'payment_method': req['payment_method'],
                        'status': req['status'],
                        'auto_decision': req['auto_decision'],
                        'auto_decision_reason': req['auto_decision_reason'],
                        'date': req['created_at'].strftime('%Y-%m-%d'),
                        'finance_approved_at': req['finance_approved_at'].strftime('%Y-%m-%d %H:%M:%S') if req['finance_approved_at'] else None,
                        'finance_confirmed_at': req['finance_confirmed_at'].strftime('%Y-%m-%d %H:%M:%S') if req['finance_confirmed_at'] else None,
                        'rejection_reason': req['rejection_reason'],
                        'rejected_at': req['rejected_at'].strftime('%Y-%m-%d %H:%M:%S') if req['rejected_at'] else None
                    })

                return result

    except Exception as e:
        print(f"❌ Error getting finance history: {e}")
        return []

def delete_request_permanently(request_id):
    """Permanently delete a request from the database"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # Delete related payments first
                cursor.execute("DELETE FROM new_payments WHERE request_id = %s", (request_id,))
                
                # Delete transcript files
                cursor.execute("DELETE FROM transcript_files WHERE request_id = %s", (request_id,))
                
                # Delete the request
                cursor.execute("DELETE FROM new_transcript_requests WHERE id = %s", (request_id,))
                
                connection.commit()
                return cursor.rowcount > 0
                
    except Exception as e:
        print(f"❌ Error deleting request: {e}")
        return False
