// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the page
    initializePage();
    
    // Set current year in footer
    document.getElementById('currentYear').textContent = new Date().getFullYear();
    
    // Initialize animations
    initializeAnimations();
    
    // Initialize counter animations
    initializeCounters();
    
    // Add scroll effects
    initializeScrollEffects();
});

// Initialize page functionality
function initializePage() {
    console.log('INES Ruhengeri Homepage Loaded');
    
    // Add smooth scrolling for anchor links
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Initialize animations
function initializeAnimations() {
    // Observe elements for scroll animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // Observe feature cards
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.2}s`;
        observer.observe(card);
    });
}

// Initialize counter animations
function initializeCounters() {
    const counters = document.querySelectorAll('.stat-number');
    const speed = 200; // Animation speed

    const countUp = (counter) => {
        const target = parseInt(counter.getAttribute('data-target'));
        const count = parseInt(counter.innerText);
        const increment = target / speed;

        if (count < target) {
            counter.innerText = Math.ceil(count + increment);
            setTimeout(() => countUp(counter), 1);
        } else {
            counter.innerText = target;
        }
    };

    // Intersection Observer for counters
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                counter.innerText = '0';
                countUp(counter);
                counterObserver.unobserve(counter); // Only animate once
            }
        });
    }, {
        threshold: 0.5
    });

    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

// Initialize scroll effects
function initializeScrollEffects() {
    let ticking = false;

    function updateScrollEffects() {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.hero-background');
        
        if (parallax) {
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        }
        
        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick);
}

// Scroll to features section
function scrollToFeatures() {
    const featuresSection = document.getElementById('features');
    if (featuresSection) {
        featuresSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Redirect to login with role parameter
function redirectToLogin(role) {
    showLoading();
    
    setTimeout(() => {
        if (role) {
            window.location.href = `login.html?role=${role}`;
        } else {
            window.location.href = 'login.html';
        }
    }, 1000);
}

// Show coming soon modal
function showComingSoon() {
    showModal(
        'Coming Soon!',
        `
        <div style="text-align: center; padding: 20px;">
            <div style="font-size: 3rem; margin-bottom: 20px;">🚧</div>
            <h4>Feature Under Development</h4>
            <p>This feature is currently under development and will be available soon.</p>
            <p>Thank you for your patience!</p>
        </div>
        `
    );
}

// Show transcript information
function showTranscriptInfo() {
    showModal(
        'Transcript Management System',
        `
        <div style="padding: 10px;">
            <h4 style="color: #4CAF50; margin-bottom: 15px;">📄 Digital Transcript Services</h4>
            
            <div style="margin-bottom: 20px;">
                <h5>🔒 Secure Processing</h5>
                <p>All transcript requests are processed through our secure digital platform with end-to-end encryption.</p>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h5>⚡ Fast Delivery</h5>
                <p>Digital transcripts are typically processed within 3-5 business days after payment confirmation.</p>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h5>💰 Affordable Pricing</h5>
                <p>Competitive pricing at 1,000 RWF per academic year transcript.</p>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h5>📱 Easy Access</h5>
                <p>Access your transcripts anytime through the student portal with download tracking.</p>
            </div>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px;">
                <strong>Ready to get started?</strong><br>
                <button onclick="closeModal(); redirectToLogin('student');" style="background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-top: 10px; cursor: pointer;">
                    Access Student Portal
                </button>
            </div>
        </div>
        `
    );
}

// Contact support function
function contactSupport() {
    showModal(
        'IT Support Contact',
        `
        <div style="padding: 10px;">
            <h4 style="color: #4CAF50; margin-bottom: 15px;">🎧 Technical Support</h4>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h5>MUHAYIMANA Jules</h5>
                <p><strong>📞 Phone:</strong> 0783076306</p>
                <p><strong>⏰ Available:</strong> Monday - Friday, 8:00 AM - 5:00 PM</p>
                <p><strong>📧 Email:</strong> <EMAIL></p>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h5>🆘 Common Issues We Help With:</h5>
                <ul style="margin-left: 20px;">
                    <li>Login problems</li>
                    <li>Password reset</li>
                    <li>Transcript request issues</li>
                    <li>Payment problems</li>
                    <li>Technical difficulties</li>
                </ul>
            </div>
            
            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px;">
                <strong>💡 Quick Tip:</strong> Have your student ID ready when contacting support for faster assistance.
            </div>
        </div>
        `
    );
}

// Modal functions
function showModal(title, content) {
    const modal = document.getElementById('modal');
    const modalTitle = document.getElementById('modal-title');
    const modalBody = document.getElementById('modal-body');
    
    modalTitle.textContent = title;
    modalBody.innerHTML = content;
    modal.style.display = 'flex';
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

function closeModal() {
    const modal = document.getElementById('modal');
    modal.style.display = 'none';
}

// Loading functions
function showLoading() {
    const loading = document.getElementById('loading');
    loading.style.display = 'flex';
}

function hideLoading() {
    const loading = document.getElementById('loading');
    loading.style.display = 'none';
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add some interactive effects
document.addEventListener('mousemove', debounce(function(e) {
    const hero = document.querySelector('.hero');
    if (hero) {
        const rect = hero.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
            const xPercent = (x / rect.width) * 100;
            const yPercent = (y / rect.height) * 100;
            
            hero.style.background = `radial-gradient(circle at ${xPercent}% ${yPercent}%, rgba(76, 175, 80, 0.1) 0%, transparent 50%)`;
        }
    }
}, 50));

// Add keyboard navigation
document.addEventListener('keydown', function(e) {
    // Add keyboard shortcuts
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case 'h':
                e.preventDefault();
                window.location.href = '#';
                break;
            case 'l':
                e.preventDefault();
                redirectToLogin();
                break;
        }
    }
});

// Add touch gestures for mobile
let touchStartY = 0;
let touchEndY = 0;

document.addEventListener('touchstart', function(e) {
    touchStartY = e.changedTouches[0].screenY;
});

document.addEventListener('touchend', function(e) {
    touchEndY = e.changedTouches[0].screenY;
    handleSwipe();
});

function handleSwipe() {
    const swipeThreshold = 50;
    const diff = touchStartY - touchEndY;
    
    if (Math.abs(diff) > swipeThreshold) {
        if (diff > 0) {
            // Swipe up - scroll to next section
            const currentSection = getCurrentSection();
            const nextSection = getNextSection(currentSection);
            if (nextSection) {
                nextSection.scrollIntoView({ behavior: 'smooth' });
            }
        }
    }
}

function getCurrentSection() {
    const sections = document.querySelectorAll('section, .hero');
    const scrollPosition = window.pageYOffset + window.innerHeight / 2;
    
    for (let section of sections) {
        const rect = section.getBoundingClientRect();
        const sectionTop = rect.top + window.pageYOffset;
        const sectionBottom = sectionTop + rect.height;
        
        if (scrollPosition >= sectionTop && scrollPosition <= sectionBottom) {
            return section;
        }
    }
    return null;
}

function getNextSection(currentSection) {
    if (!currentSection) return null;
    
    const sections = Array.from(document.querySelectorAll('section, .hero'));
    const currentIndex = sections.indexOf(currentSection);
    
    return sections[currentIndex + 1] || null;
}

// Performance optimization
window.addEventListener('load', function() {
    // Hide loading overlay if it was shown
    hideLoading();
    
    // Preload critical images
    const criticalImages = [
        'static/images/home_image.JPG',
        'static/images/footer.png',
        'static/images/ines-logo.png'
    ];
    
    criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });
});

// Error handling
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    // You could send this to a logging service
});

// Console welcome message
console.log(`
🎓 Welcome to INES Ruhengeri Transcript Management System
📧 For support: MUHAYIMANA Jules - 0783076306
🌐 Built with HTML, CSS, and JavaScript
`);
