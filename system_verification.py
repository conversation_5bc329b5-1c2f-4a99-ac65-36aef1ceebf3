#!/usr/bin/env python3
"""
Final system verification for INES Transcript System
This script verifies all components are working correctly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_database_schema():
    """Verify database schema is correct"""
    print("🔍 Verifying database schema...")
    try:
        import pymysql
        from new_database_service import DB_CONFIG
        
        connection = pymysql.connect(**DB_CONFIG)
        with connection.cursor() as cursor:
            # Check required columns exist
            cursor.execute("DESCRIBE new_transcript_requests")
            columns = [row[0] for row in cursor.fetchall()]
            
            required_columns = [
                'payment_proof_filename',
                'approved_date',
                'rejected_at',
                'rejection_reason',
                'finance_approved_by',
                'finance_rejected_by'
            ]
            
            missing_columns = []
            for col in required_columns:
                if col not in columns:
                    missing_columns.append(col)
            
            if missing_columns:
                print(f"❌ Missing columns: {missing_columns}")
                return False
            else:
                print(f"✅ All required columns present ({len(required_columns)} columns)")
                return True
                
    except Exception as e:
        print(f"❌ Database schema verification failed: {e}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()

def verify_upload_directories():
    """Verify upload directories exist"""
    print("🔍 Verifying upload directories...")
    try:
        import os
        
        required_dirs = [
            'static/uploads',
            'static/uploads/payment_proofs',
            'static/uploads/transcripts'
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                missing_dirs.append(dir_path)
        
        if missing_dirs:
            print(f"❌ Missing directories: {missing_dirs}")
            return False
        else:
            print(f"✅ All upload directories exist ({len(required_dirs)} directories)")
            return True
            
    except Exception as e:
        print(f"❌ Directory verification failed: {e}")
        return False

def verify_database_functions():
    """Verify database functions work"""
    print("🔍 Verifying database functions...")
    try:
        from new_database_service import get_finance_dashboard_data, get_requests_by_student_id
        
        # Test finance dashboard
        dashboard_data = get_finance_dashboard_data()
        print(f"✅ Finance dashboard: {dashboard_data['pending_count']} pending, {dashboard_data['approved_count']} approved")
        
        # Test student requests
        import pymysql
        from new_database_service import DB_CONFIG
        connection = pymysql.connect(**DB_CONFIG)
        with connection.cursor() as cursor:
            cursor.execute("SELECT u.reg_no FROM new_users u JOIN students s ON u.id = s.user_id LIMIT 1")
            result = cursor.fetchone()
            if result:
                student_id = result[0]
                requests = get_requests_by_student_id(student_id)
                print(f"✅ Student requests: {len(requests)} requests for {student_id}")
            else:
                print("⚠️ No students found")
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Database functions verification failed: {e}")
        return False

def verify_templates():
    """Verify template files exist"""
    print("🔍 Verifying template files...")
    try:
        import os
        
        required_templates = [
            'templates/student/view_status.html',
            'templates/student/view_history.html',
            'templates/student/payment_proof.html',
            'templates/finance/enhanced_dashboard.html'
        ]
        
        missing_templates = []
        for template in required_templates:
            if not os.path.exists(template):
                missing_templates.append(template)
        
        if missing_templates:
            print(f"❌ Missing templates: {missing_templates}")
            return False
        else:
            print(f"✅ All template files exist ({len(required_templates)} templates)")
            return True
            
    except Exception as e:
        print(f"❌ Template verification failed: {e}")
        return False

def print_user_guide():
    """Print user guide for testing the system"""
    print("\n" + "=" * 60)
    print("🎯 SYSTEM READY - USER TESTING GUIDE")
    print("=" * 60)
    
    print("\n📋 STUDENT WORKFLOW:")
    print("1. Start Flask app: python app.py")
    print("2. Go to: http://localhost:5000")
    print("3. Login as student (use existing credentials)")
    print("4. Click 'Request Transcript'")
    print("5. Fill form with academic years")
    print("6. Select payment method")
    print("7. Upload payment proof (any image/PDF)")
    print("8. Submit request")
    print("9. Check 'View Status' - should show pending request")
    
    print("\n💰 FINANCE WORKFLOW:")
    print("1. Login as finance user")
    print("2. Go to Finance Dashboard")
    print("3. Click 'View Status' in sidebar")
    print("4. See pending request with payment proof")
    print("5. Click 'Approve' or 'Reject'")
    print("6. Check 'View History' to see processed requests")
    
    print("\n📧 EMAIL NOTIFICATIONS:")
    print("- Check console logs for email sending status")
    print("- Student gets notified on submission")
    print("- Finance gets notified of new requests")
    print("- Student gets notified of approval/rejection")
    
    print("\n🔍 TROUBLESHOOTING:")
    print("- If requests don't appear: Check console for errors")
    print("- If emails don't send: Check email configuration in app.py")
    print("- If upload fails: Check static/uploads/payment_proofs permissions")

def main():
    """Run all verifications"""
    print("🚀 INES Transcript System - Final Verification")
    print("=" * 60)
    
    # Run all verifications
    schema_ok = verify_database_schema()
    dirs_ok = verify_upload_directories()
    functions_ok = verify_database_functions()
    templates_ok = verify_templates()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY:")
    print(f"   Database Schema: {'✅ READY' if schema_ok else '❌ FAILED'}")
    print(f"   Upload Directories: {'✅ READY' if dirs_ok else '❌ FAILED'}")
    print(f"   Database Functions: {'✅ READY' if functions_ok else '❌ FAILED'}")
    print(f"   Template Files: {'✅ READY' if templates_ok else '❌ FAILED'}")
    
    all_ok = all([schema_ok, dirs_ok, functions_ok, templates_ok])
    
    if all_ok:
        print("\n🎉 SYSTEM FULLY OPERATIONAL!")
        print_user_guide()
        return True
    else:
        print("\n⚠️ Some components failed verification. Please fix the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
