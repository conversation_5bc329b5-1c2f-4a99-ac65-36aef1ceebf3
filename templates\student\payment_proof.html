{% extends "base.html" %}

{% block title %}Upload Payment Proof - INES Transcript System{% endblock %}

{% block content %}
<div class="container">
    <!-- Header Section -->
    <div class="payment-proof-header">
        <div class="header-content">
            <div class="step-indicator">
                <div class="step completed">
                    <div class="step-number">1</div>
                    <div class="step-label">Request Details</div>
                </div>
                <div class="step completed">
                    <div class="step-number">2</div>
                    <div class="step-label">Payment Method</div>
                </div>
                <div class="step active">
                    <div class="step-number">3</div>
                    <div class="step-label">Payment Proof</div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-label">Submission</div>
                </div>
            </div>
            
            <h1>Upload Payment Proof</h1>
            <p class="subtitle">Please upload a screenshot of your payment transaction to complete your request</p>
        </div>
    </div>

    <!-- Payment Summary Card -->
    <div class="payment-summary-card">
        <h3><i class="fas fa-receipt"></i> Payment Summary</h3>
        <div class="summary-grid">
            <div class="summary-item">
                <span class="label">Academic Years:</span>
                <span class="value">{{ session.transcript_request.academic_years|join(', ') }}</span>
            </div>
            <div class="summary-item">
                <span class="label">Total Amount:</span>
                <span class="value amount">{{ "{:,.0f}".format(session.transcript_request.total_price) }} RWF</span>
            </div>
            <div class="summary-item">
                <span class="label">Payment Method:</span>
                <span class="value method">{{ session.transcript_request.payment_method|title }}</span>
            </div>
        </div>
    </div>

    <!-- Payment Instructions -->
    <div class="payment-instructions">
        <h3><i class="fas fa-info-circle"></i> Payment Instructions</h3>
        {% if session.transcript_request.payment_method == 'mobile_money' %}
        <div class="instruction-card momo">
            <div class="instruction-header">
                <img src="{{ url_for('static', filename='images/momo.png') }}" alt="Mobile Money" class="payment-logo">
                <h4>Mobile Money Payment</h4>
            </div>
            <div class="instruction-steps">
                <div class="step-item">
                    <span class="step-num">1</span>
                    <span>Dial *182# or use your mobile money app</span>
                </div>
                <div class="step-item">
                    <span class="step-num">2</span>
                    <span>Send {{ "{:,.0f}".format(session.transcript_request.total_price) }} RWF to <strong>INES Finance: 0788123456</strong></span>
                </div>
                <div class="step-item">
                    <span class="step-num">3</span>
                    <span>Take a screenshot of the confirmation message</span>
                </div>
                <div class="step-item">
                    <span class="step-num">4</span>
                    <span>Upload the screenshot below</span>
                </div>
            </div>
        </div>
        {% elif session.transcript_request.payment_method == 'bank_transfer' %}
        <div class="instruction-card bank">
            <div class="instruction-header">
                <i class="fas fa-university payment-icon"></i>
                <h4>Bank Transfer Payment</h4>
            </div>
            <div class="bank-details">
                <div class="bank-info">
                    <strong>Bank:</strong> Bank of Kigali<br>
                    <strong>Account Name:</strong> INES-Ruhengeri<br>
                    <strong>Account Number:</strong> ***********<br>
                    <strong>Amount:</strong> {{ "{:,.0f}".format(session.transcript_request.total_price) }} RWF
                </div>
            </div>
            <div class="instruction-steps">
                <div class="step-item">
                    <span class="step-num">1</span>
                    <span>Transfer the amount to the account above</span>
                </div>
                <div class="step-item">
                    <span class="step-num">2</span>
                    <span>Take a screenshot of the transfer confirmation</span>
                </div>
                <div class="step-item">
                    <span class="step-num">3</span>
                    <span>Upload the screenshot below</span>
                </div>
            </div>
        </div>
        {% else %}
        <div class="instruction-card cash">
            <div class="instruction-header">
                <i class="fas fa-money-bill-wave payment-icon"></i>
                <h4>Cash Payment</h4>
            </div>
            <div class="instruction-steps">
                <div class="step-item">
                    <span class="step-num">1</span>
                    <span>Visit INES Finance Office during working hours</span>
                </div>
                <div class="step-item">
                    <span class="step-num">2</span>
                    <span>Pay {{ "{:,.0f}".format(session.transcript_request.total_price) }} RWF in cash</span>
                </div>
                <div class="step-item">
                    <span class="step-num">3</span>
                    <span>Take a photo of the receipt</span>
                </div>
                <div class="step-item">
                    <span class="step-num">4</span>
                    <span>Upload the receipt photo below</span>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Upload Form -->
    <div class="upload-form-container">
        <form method="POST" action="{{ url_for('submit_payment_proof') }}" enctype="multipart/form-data" class="upload-form">
            <div class="upload-section">
                <h3><i class="fas fa-cloud-upload-alt"></i> Upload Payment Proof</h3>
                
                <div class="file-upload-area" id="fileUploadArea">
                    <div class="upload-content">
                        <i class="fas fa-image upload-icon"></i>
                        <h4>Drag & Drop your screenshot here</h4>
                        <p>or click to browse files</p>
                        <input type="file" name="payment_proof" id="paymentProof" accept="image/*" required>
                    </div>
                    <div class="file-preview" id="filePreview" style="display: none;">
                        <img id="previewImage" src="" alt="Preview">
                        <div class="file-info">
                            <span id="fileName"></span>
                            <button type="button" id="removeFile" class="remove-btn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="upload-requirements">
                    <h4><i class="fas fa-check-circle"></i> Requirements:</h4>
                    <ul>
                        <li>Image formats: JPG, PNG, or PDF</li>
                        <li>Maximum file size: 5MB</li>
                        <li>Screenshot must be clear and readable</li>
                        <li>Must show transaction amount and confirmation</li>
                    </ul>
                </div>
            </div>

            <div class="form-actions">
                <a href="{{ url_for('payment') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Payment
                </a>
                <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                    <i class="fas fa-paper-plane"></i> Submit Request
                </button>
            </div>
        </form>
    </div>

    <!-- Help Section -->
    <div class="help-section">
        <h3><i class="fas fa-question-circle"></i> Need Help?</h3>
        <div class="help-grid">
            <div class="help-item">
                <i class="fas fa-phone"></i>
                <div>
                    <strong>Call Finance Office</strong>
                    <p>+250 788 123 456</p>
                </div>
            </div>
            <div class="help-item">
                <i class="fas fa-envelope"></i>
                <div>
                    <strong>Email Support</strong>
                    <p><EMAIL></p>
                </div>
            </div>
            <div class="help-item">
                <i class="fas fa-clock"></i>
                <div>
                    <strong>Office Hours</strong>
                    <p>Mon-Fri: 8:00 AM - 5:00 PM</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Payment Proof Page Styles */
.payment-proof-header {
    background: linear-gradient(135deg, #083464, #1976D2);
    color: white;
    padding: 40px 0;
    margin: -20px -20px 30px -20px;
    border-radius: 0 0 20px 20px;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.step-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    gap: 20px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step.completed .step-number {
    background: #4CAF50;
}

.step.active .step-number {
    background: #FF9800;
    box-shadow: 0 0 20px rgba(255, 152, 0, 0.5);
}

.step-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.payment-proof-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.payment-summary-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    border-left: 5px solid #083464;
}

.payment-summary-card h3 {
    color: #083464;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.summary-item .label {
    font-weight: 500;
    color: #666;
}

.summary-item .value {
    font-weight: 700;
    color: #083464;
}

.summary-item .value.amount {
    color: #4CAF50;
    font-size: 1.1rem;
}

.payment-instructions {
    margin-bottom: 30px;
}

.payment-instructions h3 {
    color: #083464;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.instruction-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.instruction-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
}

.payment-logo {
    width: 40px;
    height: 40px;
}

.payment-icon {
    font-size: 2rem;
    color: #083464;
}

.instruction-header h4 {
    color: #083464;
    margin: 0;
    font-size: 1.2rem;
}

.instruction-steps {
    margin-top: 20px;
}

.step-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 15px;
}

.step-num {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #083464;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.bank-details {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.bank-info {
    font-size: 1rem;
    line-height: 1.6;
}

.upload-form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.upload-section h3 {
    color: #083464;
    margin-bottom: 25px;
    font-size: 1.3rem;
}

.file-upload-area {
    border: 3px dashed #083464;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    margin-bottom: 25px;
}

.file-upload-area:hover {
    border-color: #1976D2;
    background: rgba(8, 52, 100, 0.02);
}

.file-upload-area.dragover {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

.upload-content {
    pointer-events: none;
}

.upload-icon {
    font-size: 3rem;
    color: #083464;
    margin-bottom: 15px;
}

.upload-content h4 {
    color: #083464;
    margin-bottom: 10px;
}

.upload-content p {
    color: #666;
    margin: 0;
}

#paymentProof {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-preview {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.file-preview img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 10px;
}

.file-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.remove-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

.upload-requirements {
    background: #e8f5e8;
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #4CAF50;
}

.upload-requirements h4 {
    color: #2e7d32;
    margin-bottom: 15px;
}

.upload-requirements ul {
    margin: 0;
    padding-left: 20px;
}

.upload-requirements li {
    margin-bottom: 8px;
    color: #2e7d32;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
}

.btn {
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #083464, #1976D2);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(8, 52, 100, 0.3);
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.help-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.help-section h3 {
    color: #083464;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.help-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.help-item i {
    font-size: 1.5rem;
    color: #083464;
}

.help-item strong {
    color: #083464;
    display: block;
    margin-bottom: 5px;
}

.help-item p {
    margin: 0;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .step-indicator {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 15px;
    }
    
    .help-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('paymentProof');
    const uploadArea = document.getElementById('fileUploadArea');
    const filePreview = document.getElementById('filePreview');
    const uploadContent = uploadArea.querySelector('.upload-content');
    const previewImage = document.getElementById('previewImage');
    const fileName = document.getElementById('fileName');
    const removeBtn = document.getElementById('removeFile');
    const submitBtn = document.getElementById('submitBtn');

    // File upload handling
    uploadArea.addEventListener('click', () => fileInput.click());
    
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    });
    
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFile(e.target.files[0]);
        }
    });
    
    removeBtn.addEventListener('click', () => {
        fileInput.value = '';
        uploadContent.style.display = 'block';
        filePreview.style.display = 'none';
        submitBtn.disabled = true;
    });
    
    function handleFile(file) {
        // Validate file type
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
        if (!validTypes.includes(file.type)) {
            alert('Please upload a valid image file (JPG, PNG) or PDF.');
            return;
        }
        
        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB.');
            return;
        }
        
        // Show preview
        fileName.textContent = file.name;
        
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImage.src = e.target.result;
                previewImage.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            previewImage.style.display = 'none';
        }
        
        uploadContent.style.display = 'none';
        filePreview.style.display = 'flex';
        submitBtn.disabled = false;
    }
});
</script>
{% endblock %}
