<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Transcript | INES-Ruhengeri Transcript System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Faculty Profile" class="profile-image">
                <h2>{{ session.name }}</h2>
                <p>Faculty ID: {{ session.user_id }}</p>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="{{ url_for('faculty_dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                            {% if pending_count > 0 %}
                                <span class="badge">{{ pending_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('upload_transcript') }}" class="active">
                            <i class="fas fa-upload"></i> Upload Transcript
                            {% if pending_count > 0 %}
                                <span class="badge">{{ pending_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('request_history') }}">
                            <i class="fas fa-history"></i> Request History
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <div class="top-bar">
                <button class="menu-toggle" id="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}" style="margin-bottom: 15px; padding: 10px 15px; border-radius: 6px; font-size: 14px; {% if category == 'success' %}background-color: #d4edda; border-color: #c3e6cb; color: #155724;{% elif category == 'error' %}background-color: #f8d7da; border-color: #f5c6cb; color: #721c24;{% endif %}">
                            <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% elif category == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}" style="margin-right: 6px;"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <div class="dashboard-header">
                <h1>Welcome, {{ session.name }}</h1>
                <p>Upload academic transcripts for approved requests</p>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Pending Transcript Uploads</h2>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>Request ID</th>
                                    <th>Student Name</th>
                                    <th>Date</th>
                                    <th>Academic Years</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if requests %}
                                    {% for request in requests %}
                                        <tr>
                                            <td>{{ request.id }}</td>
                                            <td>{{ request.student_name }}</td>
                                            <td>{{ request.date }}</td>
                                            <td>{{ request.academic_years|join(', ') }}</td>
                                            <td>
                                                <span class="status-badge status-{{ request.status }}">
                                                    <img src="{{ url_for('static', filename='images/' + request.status + '.png') }}" alt="{{ request.status|capitalize }}" style="width: 24px; height: 24px; vertical-align: middle;">
                                                </span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button type="button" class="btn btn-sm btn-primary" onclick="showUploadModal('{{ request.id }}', '{{ request.student_name }}')">
                                                        <i class="fas fa-upload"></i> Upload
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="5" style="text-align: center;">No pending transcript uploads</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Upload Modal -->
    <div id="uploadModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000;">
        <div class="modal-content" style="max-width: 600px; margin: 100px auto; background: white; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <div class="modal-header" style="padding: 15px 20px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; font-size: 18px;">Upload Transcript</h2>
                <span class="close" onclick="closeUploadModal()" style="cursor: pointer; font-size: 24px;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px;">
                <form id="uploadForm" action="{{ url_for('upload_transcript') }}" method="POST" enctype="multipart/form-data">
                    <input type="hidden" id="request_id" name="request_id" value="">
                    
                    <div class="form-group">
                        <label>Student Name</label>
                        <p id="student_name" style="font-weight: 500;"></p>
                    </div>
                    
                    <div class="file-upload">
                        <i class="fas fa-file-pdf"></i>
                        <h3>Upload Transcript PDF</h3>
                        <p>Click to browse or drag and drop</p>
                        <input type="file" id="transcript" name="transcript" class="file-input" accept=".pdf" required>
                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('transcript').click()">
                            <i class="fas fa-folder-open"></i> Browse Files
                        </button>
                    </div>
                    
                    <div id="file-name" style="margin-top: 10px; text-align: center; display: none;">
                        <p><i class="fas fa-file-pdf"></i> <span id="selected-file"></span></p>
                    </div>
                    
                    <div class="form-group" style="margin-top: 20px; text-align: right;">
                        <button type="button" class="btn btn-secondary" onclick="closeUploadModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="uploadBtn">
                            <i class="fas fa-upload"></i> Upload Transcript
                        </button>
                    </div>

                    <!-- Upload Progress -->
                    <div id="uploadProgress" style="display: none; margin-top: 15px;">
                        <div style="text-align: center; color: #1a237e;">
                            <i class="fas fa-spinner fa-spin"></i> Uploading transcript...
                        </div>
                        <div style="width: 100%; background-color: #f0f0f0; border-radius: 10px; margin-top: 10px;">
                            <div id="progressBar" style="width: 0%; height: 20px; background-color: #1a237e; border-radius: 10px; transition: width 0.3s;"></div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        // Toggle sidebar on mobile
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
        
        // Close alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        });
        
        // Upload modal functionality
        const uploadModal = document.getElementById('uploadModal');
        const uploadForm = document.getElementById('uploadForm');
        const requestIdInput = document.getElementById('request_id');
        const studentNameElement = document.getElementById('student_name');
        const fileInput = document.getElementById('transcript');
        const fileNameDisplay = document.getElementById('file-name');
        const selectedFileElement = document.getElementById('selected-file');
        const uploadBtn = document.getElementById('uploadBtn');
        const uploadProgress = document.getElementById('uploadProgress');
        const progressBar = document.getElementById('progressBar');
        
        function showUploadModal(requestId, studentName) {
            requestIdInput.value = requestId;
            studentNameElement.textContent = studentName;
            uploadModal.style.display = 'block';
        }
        
        function closeUploadModal() {
            uploadModal.style.display = 'none';
            uploadForm.reset();
            fileNameDisplay.style.display = 'none';
            uploadProgress.style.display = 'none';
            uploadBtn.disabled = false;
            uploadBtn.innerHTML = '<i class="fas fa-upload"></i> Upload Transcript';
        }

        // Handle form submission with progress tracking
        uploadForm.addEventListener('submit', function(e) {
            // Validate file selection
            if (!fileInput.files.length) {
                e.preventDefault();
                alert('Please select a PDF file to upload.');
                return false;
            }

            // Validate file type
            const file = fileInput.files[0];
            if (!file.name.toLowerCase().endsWith('.pdf')) {
                e.preventDefault();
                alert('Please select a PDF file only.');
                return false;
            }

            // Show progress and disable button
            uploadProgress.style.display = 'block';
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';

            // Simulate progress (since we can't track actual upload progress easily)
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                progressBar.style.width = progress + '%';
            }, 200);

            // Complete progress after 2 seconds (form will submit normally)
            setTimeout(() => {
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
            }, 2000);

            // Allow normal form submission to proceed
            return true;
        });
        
        // Display selected file name
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                selectedFileElement.textContent = this.files[0].name;
                fileNameDisplay.style.display = 'block';
            } else {
                fileNameDisplay.style.display = 'none';
            }
        });
        
        // File upload area
        const fileUpload = document.querySelector('.file-upload');
        
        fileUpload.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.borderColor = '#1a237e';
            this.style.backgroundColor = 'rgba(26, 35, 126, 0.05)';
        });
        
        fileUpload.addEventListener('dragleave', function() {
            this.style.borderColor = '#ddd';
            this.style.backgroundColor = 'transparent';
        });
        
        fileUpload.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderColor = '#ddd';
            this.style.backgroundColor = 'transparent';
            
            if (e.dataTransfer.files.length > 0) {
                fileInput.files = e.dataTransfer.files;
                selectedFileElement.textContent = e.dataTransfer.files[0].name;
                fileNameDisplay.style.display = 'block';
            }
        });
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target == uploadModal) {
                closeUploadModal();
            }
        }
    </script>
</body>
</html>
