#!/usr/bin/env python3
"""
Fix payment_method column size issue
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pymysql
from new_database_service import DB_CONFIG

def fix_payment_method_column():
    """Fix payment_method column to allow longer values"""
    print("🔧 Fixing payment_method column...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Connected to database")
        
        with connection.cursor() as cursor:
            # Check current column definition
            cursor.execute("DESCRIBE new_transcript_requests")
            columns = cursor.fetchall()
            
            payment_method_info = None
            for col in columns:
                if col[0] == 'payment_method':
                    payment_method_info = col
                    break
            
            if payment_method_info:
                print(f"📋 Current payment_method definition: {payment_method_info[1]}")
                
                # Modify the column to allow longer values
                cursor.execute("""
                    ALTER TABLE new_transcript_requests 
                    MODIFY COLUMN payment_method VARCHAR(50) NOT NULL
                """)
                
                connection.commit()
                print("✅ payment_method column updated to VARCHAR(50)")
                
                # Verify the change
                cursor.execute("DESCRIBE new_transcript_requests")
                columns = cursor.fetchall()
                for col in columns:
                    if col[0] == 'payment_method':
                        print(f"📋 New payment_method definition: {col[1]}")
                        break
                
                return True
            else:
                print("❌ payment_method column not found")
                return False
                
    except Exception as e:
        print(f"❌ Error fixing payment_method column: {e}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    success = fix_payment_method_column()
    if success:
        print("🎉 Payment method column fixed successfully!")
    else:
        print("❌ Failed to fix payment method column")
    sys.exit(0 if success else 1)
