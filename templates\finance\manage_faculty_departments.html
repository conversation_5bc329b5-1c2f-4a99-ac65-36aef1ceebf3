<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage {{ faculty_name }} Departments | INES-Ruhengeri Transcript System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        .profile-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #007bff;
            margin-bottom: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .sidebar-header h2 {
            color: #fff;
            font-size: 1.2rem;
            margin: 10px 0 5px;
            font-weight: 600;
        }
        .sidebar-header p {
            color: #fff;
            font-size: 0.9rem;
            margin: 0;
        }
        .content-area {
            background: #f0f0f0;
            min-height: 500px;
            padding: 40px;
        }
        .department-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-top: 20px;
            max-width: 600px;
        }
        .department-container h3 {
            color: #333;
            font-size: 1.3rem;
            margin-bottom: 20px;
            text-align: center;
        }
        .department-container p {
            color: #666;
            text-align: center;
            margin-bottom: 30px;
            font-size: 0.9rem;
        }
        .dept-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .dept-table th {
            background: #f8f9fa;
            color: #333;
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
            border-bottom: 2px solid #e9ecef;
            font-size: 0.9rem;
        }
        .dept-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
            color: #495057;
            font-size: 0.9rem;
        }
        .dept-table tr:hover {
            background: #f8f9fa;
        }
        .edit-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 6px 15px;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .edit-btn:hover {
            background: #0056b3;
        }
        .save-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 6px 15px;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            margin-right: 5px;
            display: inline-block;
        }
        .cancel-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 6px 15px;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            display: inline-block;
        }
        .button-group {
            display: inline-flex;
            gap: 5px;
            align-items: center;
        }
        .fee-input {
            border: 1px solid #007bff;
            border-radius: 4px;
            padding: 6px 10px;
            font-size: 0.9rem;
            width: 100px;
        }
        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 20px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        .back-btn:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Finance Profile" class="profile-image">
                <h2>{{ session.name }}</h2>
                <p>Finance ID: {{ session.user_id }}</p>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="{{ url_for('finance_dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('view_status') }}">
                            <i class="fas fa-list-alt"></i> View Status
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manage_fees') }}" class="active">
                            <i class="fas fa-money-bill-wave"></i> Manage Fees
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <div class="top-bar">
                <button class="menu-toggle" id="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% elif category == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <div class="content-area">
                <h2 style="color: #333; font-size: 1.5rem; margin-bottom: 10px;">Manage Department Fees</h2>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <p style="color: #666; font-size: 1rem; margin: 0;">select a faculty to manage its department</p>
                    <a href="{{ url_for('manage_fees') }}" class="back-btn">
                        <i class="fas fa-arrow-left" style="margin-right: 8px;"></i> Back to Faculties
                    </a>
                </div>

                <div class="department-container">
                    <h3>{{ faculty_name.replace('Faculty of ', '').replace('faculty of ', '') }}</h3>
                    <p>Edit department fees for this faculty</p>
                    
                    <table class="dept-table">
                        <thead>
                            <tr>
                                <th>Department</th>
                                <th>Tuition</th>
                                <th>Option</th>
                            </tr>
                        </thead>
                        <tbody id="departments-table-body">
                            {% for department in departments %}
                            <tr data-department-name="{{ department.name }}">
                                <td>{{ department.name }}</td>
                                <td>
                                    <span class="department-fee-display">{{ department.fee }}</span>
                                    <input type="number" class="fee-input department-fee-input" value="{{ department.fee }}" style="display: none;" min="0">
                                </td>
                                <td>
                                    <button class="edit-btn edit-fee-inline" data-department-name="{{ department.name }}">Edit</button>
                                    <div class="button-group" style="display: none;">
                                        <button class="save-btn save-fee-inline" data-department-name="{{ department.name }}">Save</button>
                                        <button class="cancel-btn cancel-fee-inline">Cancel</button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    
                    {% if not departments %}
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-university" style="font-size: 2rem; color: #ccc; margin-bottom: 15px;"></i>
                        <p style="color: #666;">No departments found for this faculty.</p>
                    </div>
                    {% endif %}
                </div>
            </div>

        </main>
    </div>

    <script>
        // Toggle sidebar on mobile
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
        
        // Close alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        });

        // Department fee editing functionality
        document.getElementById('departments-table-body').addEventListener('click', function(event) {
            var target = event.target;
            var row = target.closest('tr');
            if (!row) return;

            var departmentName = row.getAttribute('data-department-name');
            var feeDisplay = row.querySelector('.department-fee-display');
            var feeInput = row.querySelector('.department-fee-input');
            var editButton = row.querySelector('.edit-fee-inline');
            var buttonGroup = row.querySelector('.button-group');

            // Handle Edit button click
            if (target.classList.contains('edit-fee-inline')) {
                feeDisplay.style.display = 'none';
                feeInput.style.display = 'inline-block';
                editButton.style.display = 'none';
                buttonGroup.style.display = 'inline-flex';
                feeInput.value = parseFloat(feeDisplay.textContent);
                feeInput.focus();
            }

            // Handle Cancel button click
            if (target.classList.contains('cancel-fee-inline')) {
                feeDisplay.style.display = 'inline';
                feeInput.style.display = 'none';
                editButton.style.display = 'inline-block';
                buttonGroup.style.display = 'none';
            }

            // Handle Save button click
            if (target.classList.contains('save-fee-inline')) {
                var newFee = feeInput.value;

                if (newFee === '' || isNaN(newFee) || parseFloat(newFee) < 0) {
                    alert('Please enter a valid non-negative fee.');
                    return;
                }

                // Create and submit a hidden form
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = '{{ url_for('manage_faculty_departments', faculty=faculty_name|urlencode) }}';

                var departmentInput = document.createElement('input');
                departmentInput.type = 'hidden';
                departmentInput.name = 'department';
                departmentInput.value = departmentName;
                form.appendChild(departmentInput);

                var feeSubmitInput = document.createElement('input');
                feeSubmitInput.type = 'hidden';
                feeSubmitInput.name = 'fee';
                feeSubmitInput.value = newFee;
                form.appendChild(feeSubmitInput);

                document.body.appendChild(form);
                form.submit();
            }
        });
    </script>
</body>
</html>
