<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Student Portal | INES-Ruhengeri Transcript System{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        .profile-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #007bff;
            margin-bottom: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .sidebar-header h2 {
            color: #fff;
            font-size: 1.2rem;
            margin: 10px 0 5px;
            font-weight: 600;
        }
        .sidebar-header p {
            color: #fff;
            font-size: 0.9rem;
            margin: 0;
        }
        .dashboard-header h1 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        .dashboard-header p {
            color: #666;
            font-size: 1rem;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Student Profile" class="profile-image">
                <h2>{{ session.name }}</h2>
                <p>Student ID: {{ session.user_id }}</p>
            </div>

            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="{{ url_for('student_dashboard') }}" class="{% if request.endpoint == 'student_dashboard' %}active{% endif %}">
                            <i class="fas fa-tachometer-alt"></i> {{ translations.dashboard or 'Dashboard' }}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('request_transcript') }}" class="{% if request.endpoint == 'request_transcript' %}active{% endif %}">
                            <i class="fas fa-file-alt"></i> {{ translations.request_transcript or 'Request Transcript' }}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('student_view_status') }}" class="{% if request.endpoint == 'student_view_status' %}active{% endif %}">
                            <i class="fas fa-eye"></i> View Status
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('student_view_history') }}" class="{% if request.endpoint == 'student_view_history' %}active{% endif %}">
                            <i class="fas fa-history"></i> View History
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('request_status') }}" class="{% if request.endpoint == 'request_status' %}active{% endif %}">
                            <i class="fas fa-tasks"></i> {{ translations.request_status or 'Request Status' }}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('view_downloads') }}" class="{% if request.endpoint == 'view_downloads' %}active{% endif %}">
                            <i class="fas fa-download"></i> {{ translations.view_downloads or 'View Downloads' }}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> {{ translations.logout or 'Logout' }}
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="top-bar">
                <button class="menu-toggle" id="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="language-selector">
                    <span style="margin-right: 10px; color: #666;">Language:</span>
                    <button class="btn btn-sm btn-outline-primary {% if current_language == 'en' %}active{% endif %}" onclick="changeLanguage('en')" style="margin-right: 5px;">EN</button>
                    <button class="btn btn-sm btn-outline-primary {% if current_language == 'fr' %}active{% endif %}" onclick="changeLanguage('fr')">FR</button>
                </div>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% elif category == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            {% block content %}{% endblock %}
        </main>
    </div>

    {% block scripts %}
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle sidebar on mobile
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        if (menuToggle) {
            menuToggle.addEventListener('click', function() {
                sidebar.classList.toggle('active');
            });
        }
        
        // Close alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        });

        // Language change function
        function changeLanguage(lang) {
            // Add visual feedback
            const button = document.querySelector(`button[onclick="changeLanguage('${lang}')"]`);
            if (button) {
                const originalText = button.innerHTML;
                button.innerHTML = '⏳';
                button.disabled = true;

                // Restore button if redirect fails
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }, 3000);
            }

            // Redirect to language change route
            window.location.href = '/set-language/' + lang;
        }

        // Common JavaScript functions
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-RW', {
                style: 'currency',
                currency: 'RWF',
                minimumFractionDigits: 0
            }).format(amount);
        }
    </script>
    {% endblock %}
</body>
</html>
