#!/usr/bin/env python3
"""
INES Transcript System - Sample Data Generator
Generates realistic sample data for testing the transcript system
"""

import pymysql
import bcrypt
import json
from datetime import datetime, timedelta
import random

# Database connection configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'ines_app',
    'password': 'ines_secure_2025!',
    'database': 'ines_transcript_system',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """Get database connection"""
    return pymysql.connect(**DB_CONFIG)

def hash_password(password):
    """Hash password using bcrypt"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def generate_sample_data():
    """Generate comprehensive sample data for the INES transcript system"""
    
    print("🚀 Starting sample data generation for INES Transcript System...")
    
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                
                # 1. FACULTIES
                print("📚 Creating faculties...")
                faculties = [
                    'Faculty of Sciences and Information Technology',
                    'Faculty of Economics Social Sciences and Management', 
                    'Faculty of Education',
                    'Faculty of Engineering and Technology',
                    'Faculty of Law and Public Administration'
                ]
                
                faculty_ids = {}
                for faculty in faculties:
                    cursor.execute("""
                        INSERT INTO faculties (name) VALUES (%s)
                        ON DUPLICATE KEY UPDATE name = VALUES(name)
                    """, (faculty,))
                    faculty_ids[faculty] = cursor.lastrowid or cursor.execute("SELECT id FROM faculties WHERE name = %s", (faculty,)) or cursor.fetchone()[0]
                
                # 2. DEPARTMENTS
                print("🏢 Creating departments...")
                departments_data = [
                    # FSIT
                    ('Computer Science', faculty_ids[faculties[0]], 1000.00),
                    ('Statistics Applied to Economy', faculty_ids[faculties[0]], 1000.00),
                    ('Biotechnologies', faculty_ids[faculties[0]], 1200.00),
                    
                    # FESSM
                    ('Cooperatives Management', faculty_ids[faculties[1]], 800.00),
                    ('Entrepreneurship and SME Management', faculty_ids[faculties[1]], 800.00),
                    ('Banking and Finance', faculty_ids[faculties[1]], 900.00),
                    
                    # FE
                    ('French and English', faculty_ids[faculties[2]], 750.00),
                    ('Mathematics and Physics', faculty_ids[faculties[2]], 750.00),
                    
                    # FET
                    ('Civil Engineering', faculty_ids[faculties[3]], 1500.00),
                    ('Architecture', faculty_ids[faculties[3]], 1500.00),
                    ('Water Engineering', faculty_ids[faculties[3]], 1200.00),
                    ('Land Survey', faculty_ids[faculties[3]], 1000.00),
                    
                    # FLPA
                    ('Law', faculty_ids[faculties[4]], 1200.00),
                    ('Public Administration', faculty_ids[faculties[4]], 1000.00)
                ]
                
                department_ids = {}
                for dept_name, faculty_id, fee in departments_data:
                    cursor.execute("""
                        INSERT INTO departments (name, faculty_id, transcript_fee) 
                        VALUES (%s, %s, %s)
                        ON DUPLICATE KEY UPDATE transcript_fee = VALUES(transcript_fee)
                    """, (dept_name, faculty_id, fee))
                    department_ids[dept_name] = cursor.lastrowid or cursor.execute("SELECT id FROM departments WHERE name = %s", (dept_name,)) or cursor.fetchone()[0]
                
                # 3. USERS (Students, Faculty, Finance)
                print("👥 Creating users...")
                
                # Finance Users
                finance_users = [
                    ('UWIMANA Finance Manager', '<EMAIL>', 'FIN001', 'finance'),
                    ('MUKAMANA Accounts Officer', '<EMAIL>', 'FIN002', 'finance'),
                    ('NIYONZIMA Bursar', '<EMAIL>', 'FIN003', 'finance')
                ]
                
                for name, email, reg_no, role in finance_users:
                    cursor.execute("""
                        INSERT INTO new_users (name, email, reg_no, role, password_hash, is_active, created_at)
                        VALUES (%s, %s, %s, %s, %s, 1, %s)
                        ON DUPLICATE KEY UPDATE role = VALUES(role), is_active = 1
                    """, (name, email, reg_no, role, hash_password('finance123'), datetime.now()))
                    
                    # Add to finance_staff table
                    user_id = cursor.lastrowid or cursor.execute("SELECT id FROM new_users WHERE reg_no = %s", (reg_no,)) or cursor.fetchone()[0]
                    cursor.execute("""
                        INSERT INTO finance_staff (user_id) VALUES (%s)
                        ON DUPLICATE KEY UPDATE user_id = VALUES(user_id)
                    """, (user_id,))
                
                # Faculty Users
                faculty_users = [
                    ('Dr. MUNYAKAZI Felix', '<EMAIL>', 'FAC001', 'faculty'),
                    ('Prof. UWIMANA Josephine', '<EMAIL>', 'FAC002', 'faculty'),
                    ('Dr. NKURUNZIZA Jean Baptiste', '<EMAIL>', 'FAC003', 'faculty'),
                    ('Eng. HABIMANA Claude', '<EMAIL>', 'FAC004', 'faculty'),
                    ('Dr. MUKAMANA Vestine', '<EMAIL>', 'FAC005', 'faculty')
                ]
                
                for name, email, reg_no, role in faculty_users:
                    cursor.execute("""
                        INSERT INTO new_users (name, email, reg_no, role, password_hash, is_active, created_at)
                        VALUES (%s, %s, %s, %s, %s, 1, %s)
                        ON DUPLICATE KEY UPDATE role = VALUES(role), is_active = 1
                    """, (name, email, reg_no, role, hash_password('faculty123'), datetime.now()))
                    
                    # Add to faculty_staff table
                    user_id = cursor.lastrowid or cursor.execute("SELECT id FROM new_users WHERE reg_no = %s", (reg_no,)) or cursor.fetchone()[0]
                    cursor.execute("""
                        INSERT INTO faculty_staff (user_id, faculty_id) VALUES (%s, %s)
                        ON DUPLICATE KEY UPDATE faculty_id = VALUES(faculty_id)
                    """, (user_id, random.choice(list(faculty_ids.values()))))
                
                connection.commit()
                print("✅ Sample data generation completed successfully!")
                print(f"   - {len(faculties)} faculties created")
                print(f"   - {len(departments_data)} departments created")
                print(f"   - {len(finance_users)} finance users created")
                print(f"   - {len(faculty_users)} faculty users created")
                
    except Exception as e:
        print(f"❌ Error generating sample data: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    generate_sample_data()
