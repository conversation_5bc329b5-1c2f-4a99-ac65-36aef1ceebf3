"""
New Database Service Layer for MySQL Integration
Replaces the old JSON/CSV-based database functions
"""
import pymysql
import json
import bcrypt
from datetime import datetime, date
from contextlib import contextmanager

# Database connection configuration with connection pooling for high concurrency
DB_CONFIG = {
    'host': 'localhost',
    'user': 'ines_app',
    'password': 'ines_secure_2025!',
    'database': 'ines_transcript_system',
    'charset': 'utf8mb4',
    'autocommit': False,
    'connect_timeout': 10,
    'read_timeout': 30,
    'write_timeout': 30,
    'max_allowed_packet': 16777216,
    'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
}

@contextmanager
def get_db_connection():
    """Get database connection with context manager"""
    connection = None
    try:
        connection = pymysql.connect(**DB_CONFIG)
        yield connection
    finally:
        if connection:
            connection.close()

def ensure_download_count_field():
    """Ensure download_count field exists and has proper default value"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # Check if download_count column exists
                cursor.execute("""
                    SELECT COLUMN_NAME
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = 'ines_transcript_system'
                    AND TABLE_NAME = 'new_transcript_requests'
                    AND COLUMN_NAME = 'download_count'
                """)

                if not cursor.fetchone():
                    # Add download_count column if it doesn't exist
                    cursor.execute("""
                        ALTER TABLE new_transcript_requests
                        ADD COLUMN download_count INT DEFAULT 0
                    """)
                    print("✅ Added download_count column to new_transcript_requests table")
                else:
                    # Update existing NULL values to 0
                    cursor.execute("""
                        UPDATE new_transcript_requests
                        SET download_count = 0
                        WHERE download_count IS NULL
                    """)
                    print("✅ Updated NULL download_count values to 0")

                connection.commit()
                return True
    except Exception as e:
        print(f"Error ensuring download_count field: {e}")
        return False

def hash_password(password):
    """Hash password using bcrypt"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def check_password(password, hashed):
    """Check password against hash"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

# ============================================================================
# USER AUTHENTICATION FUNCTIONS
# ============================================================================

def authenticate_user(email, password, department=None, role=None):
    """Authenticate user - replaces get_user_by_credentials()"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Get user with role-specific information
                if role == 'student':
                    cursor.execute("""
                        SELECT u.*, s.id as student_id, d.name as department_name, f.name as faculty_name
                        FROM new_users u
                        JOIN students s ON u.id = s.user_id
                        JOIN departments d ON s.department_id = d.id
                        JOIN faculties f ON d.faculty_id = f.id
                        WHERE u.email = %s AND u.role = %s AND u.is_active = 1
                    """, (email, role))
                elif role == 'faculty':
                    # First get the user
                    cursor.execute("""
                        SELECT * FROM new_users
                        WHERE email = %s AND role = %s AND is_active = 1
                    """, (email, role))

                    user = cursor.fetchone()
                    if user:
                        # Get faculty information
                        cursor.execute("""
                            SELECT fs.id as faculty_staff_id, f.name as faculty_name
                            FROM faculty_staff fs
                            JOIN faculties f ON fs.faculty_id = f.id
                            WHERE fs.user_id = %s
                        """, (user['id'],))

                        faculty_info = cursor.fetchone()
                        if faculty_info:
                            user['faculty_staff_id'] = faculty_info['faculty_staff_id']
                            user['faculty_name'] = faculty_info['faculty_name']
                        else:
                            user['faculty_staff_id'] = None
                            user['faculty_name'] = None
                elif role == 'finance':
                    cursor.execute("""
                        SELECT u.*, fin.id as finance_staff_id
                        FROM new_users u
                        JOIN finance_staff fin ON u.id = fin.user_id
                        WHERE u.email = %s AND u.role = %s AND u.is_active = 1
                    """, (email, role))
                else:
                    cursor.execute("""
                        SELECT * FROM new_users
                        WHERE email = %s AND role = %s AND is_active = 1
                    """, (email, role))

                    user = cursor.fetchone()

                # For faculty role, user is already fetched above
                if role != 'faculty':
                    user = cursor.fetchone()
                
                if not user or not check_password(password, user['password_hash']):
                    return None
                
                # Additional validation for students (department check)
                if role == 'student' and department and user.get('department_name') != department:
                    return None
                
                # Additional validation for faculty (faculty check)
                if role == 'faculty' and department and user.get('faculty_name') != department:
                    return None
                
                # Update last login
                cursor.execute("""
                    UPDATE new_users SET last_login = %s WHERE id = %s
                """, (datetime.now(), user['id']))
                connection.commit()
                
                # Remove password hash from returned data
                del user['password_hash']
                return user
    
    except Exception as e:
        print(f"Authentication error: {e}")
        return None

def get_user_by_id(user_id):
    """Get user by ID - replaces old function"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT u.*, 
                           s.id as student_id, s.department_id, d.name as department_name, f.name as faculty_name,
                           fs.id as faculty_staff_id, fs.faculty_id,
                           fin.id as finance_staff_id
                    FROM new_users u
                    LEFT JOIN students s ON u.id = s.user_id
                    LEFT JOIN departments d ON s.department_id = d.id
                    LEFT JOIN faculties f ON d.faculty_id = f.id
                    LEFT JOIN faculty_staff fs ON u.id = fs.user_id
                    LEFT JOIN finance_staff fin ON u.id = fin.user_id
                    WHERE u.id = %s AND u.is_active = 1
                """, (user_id,))
                
                user = cursor.fetchone()
                if user:
                    del user['password_hash']
                return user
    
    except Exception as e:
        print(f"Error getting user: {e}")
        return None

def get_user_by_reg_no(reg_no):
    """Get user by registration number"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT u.*, s.id as student_id, d.name as department_name
                    FROM new_users u
                    LEFT JOIN students s ON u.id = s.user_id
                    LEFT JOIN departments d ON s.department_id = d.id
                    WHERE u.reg_no = %s AND u.is_active = 1
                """, (reg_no,))
                
                user = cursor.fetchone()
                if user:
                    del user['password_hash']
                return user
    
    except Exception as e:
        print(f"Error getting user by reg_no: {e}")
        return None

# ============================================================================
# DEPARTMENT AND FACULTY FUNCTIONS
# ============================================================================

def get_all_departments():
    """Get all departments - replaces old function"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT d.*, f.name as faculty_name
                    FROM departments d
                    JOIN faculties f ON d.faculty_id = f.id
                    ORDER BY f.name, d.name
                """)
                return cursor.fetchall()
    
    except Exception as e:
        print(f"Error getting departments: {e}")
        return []

def get_all_faculties():
    """Get all faculties"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT * FROM faculties ORDER BY name")
                return cursor.fetchall()
    
    except Exception as e:
        print(f"Error getting faculties: {e}")
        return []

def get_department_fee(department_name):
    """Get transcript fee for a department"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # First check if the departments table exists and has the transcript_fee column
                cursor.execute("SHOW COLUMNS FROM departments LIKE 'transcript_fee'")
                column_exists = cursor.fetchone()

                if not column_exists:
                    print(f"⚠️ transcript_fee column does not exist in departments table")
                    return 750000.0

                cursor.execute("""
                    SELECT transcript_fee FROM departments WHERE name = %s
                """, (department_name,))
                result = cursor.fetchone()

                if result:
                    fee_value = result[0]
                    print(f"🔍 Raw fee value for {department_name}: {fee_value} (type: {type(fee_value)})")

                    # Handle different data types including decimal.Decimal
                    if fee_value is None:
                        print(f"⚠️ Fee is NULL for department {department_name}")
                        return 750000.0
                    elif isinstance(fee_value, (int, float)):
                        return float(fee_value)
                    elif hasattr(fee_value, '__float__'):  # Handles decimal.Decimal and similar types
                        return float(fee_value)
                    elif isinstance(fee_value, str):
                        if fee_value.replace('.', '').isdigit():
                            return float(fee_value)
                        else:
                            print(f"⚠️ Invalid fee format for {department_name}: '{fee_value}'")
                            return 750000.0
                    else:
                        print(f"⚠️ Unexpected fee data type for {department_name}: {type(fee_value)}")
                        return 750000.0
                else:
                    print(f"⚠️ No department found with name: {department_name}")
                    return 750000.0

    except Exception as e:
        print(f"❌ Error getting department fee for {department_name}: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return 750000.0

def update_department_fee(department_name, new_fee):
    """Update transcript fee for a department"""
    try:
        print(f"🔧 Attempting to update {department_name} fee to {new_fee}")
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # First check if department exists
                cursor.execute("SELECT id, transcript_fee FROM departments WHERE name = %s", (department_name,))
                result = cursor.fetchone()

                if not result:
                    print(f"❌ No department found with name: {department_name}")
                    # Let's see what departments exist
                    cursor.execute("SELECT name FROM departments LIMIT 10")
                    existing_depts = cursor.fetchall()
                    print(f"📋 Available departments: {[dept[0] for dept in existing_depts]}")
                    return False

                dept_id, current_fee = result
                print(f"📊 Found department {department_name} (ID: {dept_id}) with current fee: {current_fee}")

                # Update the fee
                cursor.execute("""
                    UPDATE departments SET transcript_fee = %s WHERE name = %s
                """, (float(new_fee), department_name))

                # Commit the transaction
                connection.commit()

                # Verify the update
                cursor.execute("SELECT transcript_fee FROM departments WHERE name = %s", (department_name,))
                updated_result = cursor.fetchone()

                if updated_result and float(updated_result[0]) == float(new_fee):
                    print(f"✅ Successfully updated {department_name} fee from {current_fee} to {updated_result[0]}")
                    return True
                else:
                    print(f"❌ Update verification failed for {department_name}")
                    return False

    except Exception as e:
        print(f"❌ Error updating department fee: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_departments_by_faculty():
    """Get all departments grouped by faculty for manage fees page"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # First check if the transcript_fee column exists
                cursor.execute("SHOW COLUMNS FROM departments LIKE 'transcript_fee'")
                column_exists = cursor.fetchone()

                if not column_exists:
                    print(f"⚠️ transcript_fee column does not exist in departments table")
                    # Return departments with default fee
                    cursor.execute("""
                        SELECT d.name as department_name, f.name as faculty_name
                        FROM departments d
                        JOIN faculties f ON d.faculty_id = f.id
                        ORDER BY f.name, d.name
                    """)
                    departments = cursor.fetchall()
                    faculty_departments = {}

                    for dept in departments:
                        faculty_name = dept['faculty_name']
                        if faculty_name not in faculty_departments:
                            faculty_departments[faculty_name] = []
                        faculty_departments[faculty_name].append({
                            'name': dept['department_name'],
                            'fee': 750000.0  # Default fee
                        })
                    return faculty_departments

                cursor.execute("""
                    SELECT d.name as department_name, d.transcript_fee, f.name as faculty_name
                    FROM departments d
                    JOIN faculties f ON d.faculty_id = f.id
                    ORDER BY f.name, d.name
                """)

                departments = cursor.fetchall()
                faculty_departments = {}

                for dept in departments:
                    faculty_name = dept['faculty_name']
                    if faculty_name not in faculty_departments:
                        faculty_departments[faculty_name] = []

                    # Handle transcript_fee safely including decimal.Decimal
                    fee_value = dept['transcript_fee']
                    try:
                        if fee_value is None:
                            fee = 750000.0
                        elif isinstance(fee_value, (int, float)):
                            fee = float(fee_value)
                        elif hasattr(fee_value, '__float__'):  # Handles decimal.Decimal and similar types
                            fee = float(fee_value)
                        elif isinstance(fee_value, str) and fee_value.replace('.', '').isdigit():
                            fee = float(fee_value)
                        else:
                            print(f"⚠️ Invalid fee format for {dept['department_name']}: '{fee_value}'")
                            fee = 750000.0
                    except (ValueError, TypeError) as e:
                        print(f"⚠️ Error converting fee for {dept['department_name']}: {e}")
                        fee = 750000.0

                    faculty_departments[faculty_name].append({
                        'name': dept['department_name'],
                        'fee': fee
                    })

                return faculty_departments

    except Exception as e:
        print(f"❌ Error getting departments by faculty: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return {}

# ============================================================================
# TRANSCRIPT REQUEST FUNCTIONS
# ============================================================================

def add_request(student_reg_no, academic_years, payment_method, total_price, **kwargs):
    """Add new transcript request - optimized for high concurrency"""
    connection = None
    try:
        connection = pymysql.connect(**DB_CONFIG)

        # Start transaction with proper isolation
        connection.begin()

        with connection.cursor() as cursor:
            # Get student ID with shared lock
            cursor.execute("""
                SELECT s.id FROM students s
                JOIN new_users u ON s.user_id = u.id
                WHERE u.reg_no = %s
                LOCK IN SHARE MODE
            """, (student_reg_no,))
            result = cursor.fetchone()
            if not result:
                connection.rollback()
                return None

            student_id = result[0]

            # Generate unique request number with database-level atomicity
            current_year = datetime.now().year
            max_retries = 5

            for attempt in range(max_retries):
                try:
                    # Use database lock to ensure atomic number generation
                    cursor.execute("SELECT GET_LOCK('request_number_generation', 10)")
                    lock_result = cursor.fetchone()

                    if not lock_result or lock_result[0] != 1:
                        if attempt == max_retries - 1:
                            # Fallback to timestamp-based number
                            import time
                            request_number = f"REQ-{current_year}-{int(time.time())}-{student_id}"
                            break
                        continue

                    try:
                        # Get the highest existing request number for this year
                        cursor.execute("""
                            SELECT COALESCE(MAX(CAST(SUBSTRING_INDEX(request_number, '-', -1) AS UNSIGNED)), 0) as max_num
                            FROM new_transcript_requests
                            WHERE request_number REGEXP %s
                        """, (f"^REQ-{current_year}-[0-9]+$",))

                        result = cursor.fetchone()
                        max_number = result[0] if result and result[0] else 0
                        next_number = max_number + 1

                        # Generate the request number
                        request_number = f"REQ-{current_year}-{next_number:04d}"

                        # Verify uniqueness one more time
                        cursor.execute("SELECT COUNT(*) FROM new_transcript_requests WHERE request_number = %s", (request_number,))
                        if cursor.fetchone()[0] == 0:
                            break  # Success - unique number found

                    finally:
                        # Always release the lock
                        cursor.execute("SELECT RELEASE_LOCK('request_number_generation')")

                    if attempt == max_retries - 1:
                        # Final fallback
                        import time
                        import random
                        request_number = f"REQ-{current_year}-{int(time.time())}-{random.randint(100, 999)}"

                except Exception as e:
                    print(f"Attempt {attempt + 1} failed: {e}")
                    if attempt == max_retries - 1:
                        import time
                        request_number = f"REQ-{current_year}-{int(time.time())}-FALLBACK"

            # Insert request with retry logic for deadlocks
            max_insert_retries = 3
            for insert_attempt in range(max_insert_retries):
                try:
                    cursor.execute("""
                        INSERT INTO new_transcript_requests
                        (student_id, request_number, academic_years, total_transcripts, total_amount,
                         payment_method, status, purpose, institution_name, additional_notes, payment_proof_filename, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    """, (
                        student_id, request_number, json.dumps(academic_years),
                        len(academic_years), total_price, payment_method, 'pending_finance',
                        kwargs.get('purpose'), kwargs.get('institution_name'), kwargs.get('additional_notes'),
                        kwargs.get('payment_proof_filename')
                    ))

                    request_id = cursor.lastrowid

                    # Commit the transaction
                    connection.commit()

                    print(f"✅ Request created successfully: ID {request_id}, Number {request_number}")

                    return {
                        'id': request_id,
                        'request_number': request_number,
                        'student_id': student_reg_no,
                        'academic_years': academic_years,
                        'count': len(academic_years),
                        'total_price': total_price,
                        'payment_method': payment_method,
                        'status': 'pending_finance',
                        'date': datetime.now().strftime('%Y-%m-%d')
                    }

                except pymysql.err.IntegrityError as ie:
                    if "Duplicate entry" in str(ie) and insert_attempt < max_insert_retries - 1:
                        print(f"⚠️ Duplicate detected on attempt {insert_attempt + 1}, retrying...")
                        connection.rollback()
                        # Generate new number and retry
                        import time
                        import random
                        request_number = f"REQ-{current_year}-{int(time.time())}-{random.randint(100, 999)}"
                        continue
                    else:
                        raise ie
                except pymysql.err.OperationalError as oe:
                    if "Deadlock" in str(oe) and insert_attempt < max_insert_retries - 1:
                        print(f"⚠️ Deadlock detected on attempt {insert_attempt + 1}, retrying...")
                        connection.rollback()
                        import time
                        time.sleep(0.1 * (insert_attempt + 1))  # Exponential backoff
                        continue
                    else:
                        raise oe

                break  # Success, exit retry loop

    except Exception as e:
        print(f"❌ Error adding request: {e}")
        if connection:
            try:
                connection.rollback()
            except:
                pass
        return None

    finally:
        if connection:
            try:
                connection.close()
            except:
                pass

def get_requests_by_student_id(student_reg_no):
    """Get requests by student - replaces old function"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT tr.*, u.reg_no as student_id, u.name as student_name
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    WHERE u.reg_no = %s
                    ORDER BY tr.created_at DESC
                """, (student_reg_no,))
                
                requests = cursor.fetchall()
                
                # Convert to old format for compatibility
                result = []
                for req in requests:
                    result.append({
                        'id': str(req['id']),
                        'student_id': req['student_id'],
                        'student_name': req['student_name'],
                        'academic_years': json.loads(req['academic_years']),
                        'count': req['total_transcripts'],
                        'total_price': float(req['total_amount']),
                        'payment_method': req['payment_method'],
                        'status': req['status'],
                        'date': req['created_at'].strftime('%Y-%m-%d'),
                        'rejection_reason': req['rejection_reason'],
                        'rejected_at': req['rejected_at'].strftime('%Y-%m-%d %H:%M:%S') if req['rejected_at'] else None,
                        'download_count': req.get('download_count', 0),
                        'last_downloaded': req['last_downloaded'].strftime('%Y-%m-%d %H:%M:%S') if req.get('last_downloaded') else None,
                        'approved_date': req['finance_approved_at'].strftime('%Y-%m-%d') if req.get('finance_approved_at') else req['created_at'].strftime('%Y-%m-%d'),
                        'finance_confirmed_at': req['finance_confirmed_at'].strftime('%Y-%m-%d %H:%M:%S') if req.get('finance_confirmed_at') else None
                    })
                
                return result
    
    except Exception as e:
        print(f"Error getting student requests: {e}")
        return []

def get_all_requests():
    """Get all requests - replaces old function"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT tr.*, u.reg_no as student_id, u.name as student_name, 
                           d.name as department, f.name as faculty
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    JOIN departments d ON s.department_id = d.id
                    JOIN faculties f ON d.faculty_id = f.id
                    ORDER BY tr.created_at DESC
                """)
                
                requests = cursor.fetchall()
                
                # Convert to old format
                result = []
                for req in requests:
                    result.append({
                        'id': str(req['id']),
                        'student_id': req['student_id'],
                        'student_name': req['student_name'],
                        'department': req['department'],
                        'faculty': req['faculty'],
                        'academic_years': json.loads(req['academic_years']),
                        'count': req['total_transcripts'],
                        'total_price': float(req['total_amount']),
                        'payment_method': req['payment_method'],
                        'status': req['status'],
                        'date': req['created_at'].strftime('%Y-%m-%d'),
                        'rejection_reason': req['rejection_reason'],
                        'rejected_at': req['rejected_at'].strftime('%Y-%m-%d %H:%M:%S') if req['rejected_at'] else None,
                        'download_count': req.get('download_count', 0),
                        'last_downloaded': req['last_downloaded'].strftime('%Y-%m-%d %H:%M:%S') if req.get('last_downloaded') else None,
                        'approved_date': req['finance_approved_at'].strftime('%Y-%m-%d') if req.get('finance_approved_at') else req['created_at'].strftime('%Y-%m-%d'),
                        'finance_confirmed_at': req['finance_confirmed_at'].strftime('%Y-%m-%d %H:%M:%S') if req.get('finance_confirmed_at') else None
                    })
                
                return result
    
    except Exception as e:
        print(f"Error getting all requests: {e}")
        return []

def update_request_status(request_id, status, rejection_reason=None, rejected_by=None):
    """Update request status - replaces old function"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                if status == 'rejected':
                    cursor.execute("""
                        UPDATE new_transcript_requests
                        SET status = %s, rejection_reason = %s, rejected_at = %s, rejected_by = %s
                        WHERE id = %s
                    """, (status, rejection_reason, datetime.now(), rejected_by, request_id))
                elif status == 'approved_finance':
                    cursor.execute("""
                        UPDATE new_transcript_requests
                        SET status = %s, finance_approved_at = %s
                        WHERE id = %s
                    """, (status, datetime.now(), request_id))
                else:
                    cursor.execute("""
                        UPDATE new_transcript_requests
                        SET status = %s
                        WHERE id = %s
                    """, (status, request_id))

                connection.commit()
                return cursor.rowcount > 0

    except Exception as e:
        print(f"Error updating request status: {e}")
        return False

def track_transcript_download(request_id, student_reg_no):
    """Track transcript download to prevent multiple downloads - ATOMIC OPERATION"""
    connection = None
    try:
        # Use direct connection for better transaction control
        connection = pymysql.connect(**DB_CONFIG)

        # Start transaction with proper isolation level
        connection.begin()

        with connection.cursor() as cursor:
            # Lock the row for update to prevent race conditions
            cursor.execute("""
                SELECT tr.id, tr.status, tr.download_count, u.reg_no
                FROM new_transcript_requests tr
                JOIN students s ON tr.student_id = s.id
                JOIN new_users u ON s.user_id = u.id
                WHERE tr.id = %s AND u.reg_no = %s
                FOR UPDATE
            """, (request_id, student_reg_no))

            result = cursor.fetchone()
            if not result:
                print(f"❌ Request {request_id} not found for student {student_reg_no}")
                connection.rollback()
                return False

            request_id_db, status, current_downloads, student_reg_no_db = result
            current_downloads = current_downloads or 0  # Handle NULL

            print(f"🔒 LOCKED: Request {request_id}, Status: {status}, Downloads: {current_downloads}")

            # Check if already downloaded
            if current_downloads > 0:
                print(f"❌ Request {request_id} already downloaded {current_downloads} time(s)")
                connection.rollback()
                return False

            # Check if status allows download
            if status not in ['completed']:
                print(f"❌ Request {request_id} status '{status}' does not allow download")
                connection.rollback()
                return False

            # Update download tracking and change status to 'done' atomically
            cursor.execute("""
                UPDATE new_transcript_requests
                SET download_count = COALESCE(download_count, 0) + 1,
                    last_downloaded = %s,
                    status = 'done'
                WHERE id = %s
            """, (datetime.now(), request_id))

            # Verify the update
            if cursor.rowcount != 1:
                print(f"❌ Failed to update download tracking for request {request_id} - rowcount: {cursor.rowcount}")
                connection.rollback()
                return False

            # Commit the transaction
            connection.commit()
            print(f"✅ Download tracked successfully for request {request_id} - Status changed to 'done'")
            return True

    except Exception as e:
        print(f"❌ Error tracking download: {e}")
        if connection:
            connection.rollback()
        return False
    finally:
        if connection:
            connection.close()

def delete_request_after_download(request_id, student_reg_no):
    """Delete transcript request completely after successful download"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # Verify the request belongs to the student and is completed
                cursor.execute("""
                    SELECT tr.id, tr.status, u.reg_no
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    WHERE tr.id = %s AND u.reg_no = %s AND tr.status = 'completed'
                """, (request_id, student_reg_no))

                result = cursor.fetchone()
                if not result:
                    print(f"❌ Request {request_id} not found or not in completed status for student {student_reg_no}")
                    return False

                print(f"🗑️ Deleting request {request_id} after successful download...")

                # Delete related transcript files first
                cursor.execute("""
                    DELETE FROM transcript_files WHERE request_id = %s
                """, (request_id,))
                print(f"   - Deleted transcript files for request {request_id}")

                # Delete related payments
                cursor.execute("""
                    DELETE FROM new_payments WHERE request_id = %s
                """, (request_id,))
                print(f"   - Deleted payments for request {request_id}")

                # Delete the main request
                cursor.execute("""
                    DELETE FROM new_transcript_requests WHERE id = %s
                """, (request_id,))
                print(f"   - Deleted main request {request_id}")

                connection.commit()
                print(f"✅ Request {request_id} completely deleted after download")
                return True

    except Exception as e:
        print(f"❌ Error deleting request after download: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

# ============================================================================
# ENHANCED FINANCE DASHBOARD FUNCTIONS
# ============================================================================

def get_finance_dashboard_data():
    """Get comprehensive finance dashboard data with enhanced information"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # Get pending requests with detailed information
                cursor.execute("""
                    SELECT
                        tr.id,
                        tr.request_number,
                        tr.academic_years,
                        tr.total_amount,
                        tr.payment_method,
                        tr.payment_proof_filename,
                        tr.created_at as date,
                        tr.status,
                        u.name as student_name,
                        u.reg_no as student_id,
                        d.name as department,
                        COALESCE(sf.amount_paid, 0) as school_fees_paid,
                        COALESCE(sf.total_fees_required, 0) as total_fees_required
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    JOIN departments d ON s.department_id = d.id
                    LEFT JOIN school_fees_payments sf ON s.id = sf.student_id
                        AND JSON_CONTAINS(tr.academic_years, CONCAT('"', sf.academic_year, '"'))
                    WHERE tr.status = 'pending_finance'
                    ORDER BY tr.created_at DESC
                """)

                pending_requests = []
                for row in cursor.fetchall():
                    request_data = {
                        'id': row[0],
                        'request_number': row[1],
                        'academic_years': json.loads(row[2]) if row[2] else [],
                        'total_amount': float(row[3]) if row[3] else 0,
                        'payment_method': row[4],
                        'payment_proof_filename': row[5],
                        'date': row[6],
                        'status': row[7],
                        'student_name': row[8],
                        'student_id': row[9],
                        'department': row[10],
                        'school_fees_paid': float(row[11]) if row[11] else 0,
                        'total_fees_required': float(row[12]) if row[12] else 0
                    }
                    pending_requests.append(request_data)

                # Get approved requests
                cursor.execute("""
                    SELECT
                        tr.id,
                        tr.request_number,
                        tr.academic_years,
                        tr.total_amount,
                        tr.payment_method,
                        tr.created_at as date,
                        tr.approved_date,
                        tr.status,
                        u.name as student_name
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    WHERE tr.status IN ('approved_finance', 'faculty_processing', 'completed')
                    ORDER BY tr.approved_date DESC
                """)

                approved_requests = []
                for row in cursor.fetchall():
                    request_data = {
                        'id': row[0],
                        'request_number': row[1],
                        'academic_years': json.loads(row[2]) if row[2] else [],
                        'total_amount': float(row[3]) if row[3] else 0,
                        'payment_method': row[4],
                        'date': row[5],
                        'approved_date': row[6],
                        'status': row[7],
                        'student_name': row[8]
                    }
                    approved_requests.append(request_data)

                # Get rejected requests
                cursor.execute("""
                    SELECT
                        tr.id,
                        tr.request_number,
                        tr.academic_years,
                        tr.total_amount,
                        tr.payment_method,
                        tr.created_at as date,
                        tr.rejected_at,
                        tr.rejection_reason,
                        u.name as student_name
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    WHERE tr.status = 'rejected'
                    ORDER BY tr.rejected_at DESC
                """)

                rejected_requests = []
                for row in cursor.fetchall():
                    request_data = {
                        'id': row[0],
                        'request_number': row[1],
                        'academic_years': json.loads(row[2]) if row[2] else [],
                        'total_amount': float(row[3]) if row[3] else 0,
                        'payment_method': row[4],
                        'date': row[5],
                        'rejected_at': row[6],
                        'rejection_reason': row[7],
                        'student_name': row[8]
                    }
                    rejected_requests.append(request_data)

                # Calculate counts
                pending_count = len(pending_requests)
                approved_count = len(approved_requests)
                rejected_count = len(rejected_requests)
                total_count = pending_count + approved_count + rejected_count

                return {
                    'pending_requests': pending_requests,
                    'approved_requests': approved_requests,
                    'rejected_requests': rejected_requests,
                    'pending_count': pending_count,
                    'approved_count': approved_count,
                    'rejected_count': rejected_count,
                    'total_count': total_count
                }

    except Exception as e:
        print(f"❌ Error getting finance dashboard data: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return {
            'pending_requests': [],
            'approved_requests': [],
            'rejected_requests': [],
            'pending_count': 0,
            'approved_count': 0,
            'rejected_count': 0,
            'total_count': 0
        }

def approve_transcript_request(request_id, finance_user_id):
    """Approve a transcript request and notify student and faculty"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # Get request details for notifications
                cursor.execute("""
                    SELECT tr.id, tr.student_id, u.name as student_name, u.email as student_email,
                           tr.academic_years, tr.total_amount, tr.payment_method
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    WHERE tr.id = %s AND tr.status = 'pending_finance'
                """, (request_id,))

                request_data = cursor.fetchone()
                if not request_data:
                    print(f"❌ Request {request_id} not found or not in pending status")
                    return False

                # Update request status to approved
                cursor.execute("""
                    UPDATE new_transcript_requests
                    SET status = 'approved_finance',
                        approved_date = NOW(),
                        finance_approved_by = %s
                    WHERE id = %s
                """, (finance_user_id, request_id))

                connection.commit()

                # Send notifications (implement email notifications here)
                print(f"✅ Request {request_id} approved successfully")
                return True

    except Exception as e:
        print(f"❌ Error approving request {request_id}: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

def reject_transcript_request(request_id, finance_user_id, reason="Payment verification failed"):
    """Reject a transcript request and notify student"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # Get request details for notifications
                cursor.execute("""
                    SELECT tr.id, tr.student_id, u.name as student_name, u.email as student_email,
                           tr.academic_years, tr.total_amount, tr.payment_method
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    WHERE tr.id = %s AND tr.status = 'pending_finance'
                """, (request_id,))

                request_data = cursor.fetchone()
                if not request_data:
                    print(f"❌ Request {request_id} not found or not in pending status")
                    return False

                # Update request status to rejected
                cursor.execute("""
                    UPDATE new_transcript_requests
                    SET status = 'rejected',
                        rejected_at = NOW(),
                        rejection_reason = %s,
                        finance_rejected_by = %s
                    WHERE id = %s
                """, (reason, finance_user_id, request_id))

                connection.commit()

                # Send notifications (implement email notifications here)
                print(f"✅ Request {request_id} rejected successfully")
                return True

    except Exception as e:
        print(f"❌ Error rejecting request {request_id}: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

# ============================================================================
# FINANCE DASHBOARD FUNCTIONS
# ============================================================================

def get_pending_requests_for_finance():
    """Get all pending requests for finance dashboard - includes both pending_finance and pending_confirmation"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT tr.*, u.name as student_name, u.reg_no as student_id, u.email,
                           d.name as department, f.name as faculty
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    JOIN departments d ON s.department_id = d.id
                    JOIN faculties f ON d.faculty_id = f.id
                    WHERE tr.status IN ('pending_finance', 'pending_confirmation')
                    ORDER BY tr.created_at ASC
                """)

                requests = cursor.fetchall()

                # Convert to format expected by templates
                result = []
                for req in requests:
                    result.append({
                        'id': str(req['id']),
                        'student_id': req['student_id'],
                        'student_name': req['student_name'],
                        'email': req['email'],
                        'department': req['department'],
                        'faculty': req['faculty'],
                        'academic_years': json.loads(req['academic_years']),
                        'count': req['total_transcripts'],
                        'total_price': float(req['total_amount']),
                        'payment_method': req['payment_method'],
                        'status': req['status'],
                        'auto_decision': req.get('auto_decision'),
                        'auto_decision_reason': req.get('auto_decision_reason'),
                        'date': req['created_at'].strftime('%Y-%m-%d'),
                        'created_at': req['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    })

                return result

    except Exception as e:
        print(f"❌ Error getting pending requests for finance: {e}")
        return []

def get_approved_requests_for_finance():
    """Get all approved requests for finance dashboard"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT tr.*, u.name as student_name, u.reg_no as student_id, u.email,
                           d.name as department, f.name as faculty
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    JOIN departments d ON s.department_id = d.id
                    JOIN faculties f ON d.faculty_id = f.id
                    WHERE tr.status IN ('approved_finance', 'faculty_processing', 'completed', 'done')
                    ORDER BY tr.finance_approved_at DESC
                """)

                requests = cursor.fetchall()

                # Convert to format expected by templates
                result = []
                for req in requests:
                    result.append({
                        'id': str(req['id']),
                        'student_id': req['student_id'],
                        'student_name': req['student_name'],
                        'email': req['email'],
                        'department': req['department'],
                        'faculty': req['faculty'],
                        'academic_years': json.loads(req['academic_years']),
                        'count': req['total_transcripts'],
                        'total_price': float(req['total_amount']),
                        'payment_method': req['payment_method'],
                        'status': req['status'],
                        'date': req['created_at'].strftime('%Y-%m-%d'),
                        'approval_date': req['finance_approved_at'].strftime('%Y-%m-%d') if req['finance_approved_at'] else None,
                        'download_count': req.get('download_count', 0),
                        'last_downloaded': req['last_downloaded'].strftime('%Y-%m-%d %H:%M:%S') if req.get('last_downloaded') else None
                    })

                return result

    except Exception as e:
        print(f"❌ Error getting approved requests for finance: {e}")
        return []

def get_rejected_requests_for_finance():
    """Get all rejected requests for finance dashboard"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT tr.*, u.name as student_name, u.reg_no as student_id, u.email,
                           d.name as department, f.name as faculty
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    JOIN departments d ON s.department_id = d.id
                    JOIN faculties f ON d.faculty_id = f.id
                    WHERE tr.status = 'rejected'
                    ORDER BY tr.rejected_at DESC
                """)

                requests = cursor.fetchall()

                # Convert to format expected by templates
                result = []
                for req in requests:
                    result.append({
                        'id': str(req['id']),
                        'student_id': req['student_id'],
                        'student_name': req['student_name'],
                        'email': req['email'],
                        'department': req['department'],
                        'faculty': req['faculty'],
                        'academic_years': json.loads(req['academic_years']),
                        'count': req['total_transcripts'],
                        'total_price': float(req['total_amount']),
                        'payment_method': req['payment_method'],
                        'status': req['status'],
                        'date': req['created_at'].strftime('%Y-%m-%d'),
                        'rejection_reason': req['rejection_reason'],
                        'rejected_at': req['rejected_at'].strftime('%Y-%m-%d %H:%M:%S') if req['rejected_at'] else None
                    })

                return result

    except Exception as e:
        print(f"❌ Error getting rejected requests for finance: {e}")
        return []

def get_pending_requests_for_faculty():
    """Get all requests pending faculty processing"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT tr.*, u.name as student_name, u.reg_no as student_id, u.email,
                           d.name as department, f.name as faculty
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    JOIN departments d ON s.department_id = d.id
                    JOIN faculties f ON d.faculty_id = f.id
                    WHERE tr.status IN ('approved_finance', 'faculty_processing')
                    ORDER BY tr.finance_approved_at ASC
                """)

                requests = cursor.fetchall()

                # Convert to format expected by templates
                result = []
                for req in requests:
                    result.append({
                        'id': str(req['id']),
                        'student_id': req['student_id'],
                        'student_name': req['student_name'],
                        'email': req['email'],
                        'department': req['department'],
                        'faculty': req['faculty'],
                        'academic_years': json.loads(req['academic_years']),
                        'count': req['total_transcripts'],
                        'total_price': float(req['total_amount']),
                        'payment_method': req['payment_method'],
                        'status': req['status'],
                        'date': req['created_at'].strftime('%Y-%m-%d'),
                        'approval_date': req['finance_approved_at'].strftime('%Y-%m-%d') if req['finance_approved_at'] else None
                    })

                return result

    except Exception as e:
        print(f"❌ Error getting pending requests for faculty: {e}")
        return []

def get_completed_requests_for_faculty():
    """Get all completed requests for faculty dashboard"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT tr.*, u.name as student_name, u.reg_no as student_id, u.email,
                           d.name as department, f.name as faculty
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    JOIN departments d ON s.department_id = d.id
                    JOIN faculties f ON d.faculty_id = f.id
                    WHERE tr.status IN ('completed', 'done')
                    ORDER BY tr.completed_at DESC
                """)

                requests = cursor.fetchall()

                # Convert to format expected by templates
                result = []
                for req in requests:
                    result.append({
                        'id': str(req['id']),
                        'student_id': req['student_id'],
                        'student_name': req['student_name'],
                        'email': req['email'],
                        'department': req['department'],
                        'faculty': req['faculty'],
                        'academic_years': json.loads(req['academic_years']),
                        'count': req['total_transcripts'],
                        'total_price': float(req['total_amount']),
                        'payment_method': req['payment_method'],
                        'status': req['status'],
                        'date': req['created_at'].strftime('%Y-%m-%d'),
                        'completed_at': req['completed_at'].strftime('%Y-%m-%d %H:%M:%S') if req.get('completed_at') else None,
                        'download_count': req.get('download_count', 0),
                        'last_downloaded': req['last_downloaded'].strftime('%Y-%m-%d %H:%M:%S') if req.get('last_downloaded') else None
                    })

                return result

    except Exception as e:
        print(f"❌ Error getting completed requests for faculty: {e}")
        return []

# ============================================================================
# PAYMENT FUNCTIONS
# ============================================================================

def add_payment(student_reg_no, academic_year, amount, payment_method, request_id, department):
    """Add payment record - replaces old function"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # Get student ID
                cursor.execute("""
                    SELECT s.id FROM students s
                    JOIN new_users u ON s.user_id = u.id
                    WHERE u.reg_no = %s
                """, (student_reg_no,))
                result = cursor.fetchone()
                if not result:
                    return False

                student_id = result[0]

                # Insert payment
                cursor.execute("""
                    INSERT INTO new_payments
                    (request_id, student_id, amount, payment_method, payment_status, payment_date)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (request_id, student_id, amount, payment_method, 'completed', datetime.now()))

                connection.commit()
                return True

    except Exception as e:
        print(f"Error adding payment: {e}")
        return False

def get_payments_by_student_id(student_reg_no):
    """Get payments by student - replaces old function"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT p.*, u.reg_no as student_id, d.name as department
                    FROM new_payments p
                    JOIN students s ON p.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    JOIN departments d ON s.department_id = d.id
                    WHERE u.reg_no = %s
                    ORDER BY p.payment_date DESC
                """, (student_reg_no,))

                payments = cursor.fetchall()

                # Convert to old format
                result = []
                for pay in payments:
                    result.append({
                        'student_id': pay['student_id'],
                        'academic_year': 'N/A',  # Not stored separately in new schema
                        'amount': float(pay['amount']),
                        'payment_method': pay['payment_method'],
                        'date': pay['payment_date'].strftime('%Y-%m-%d %H:%M:%S'),
                        'request_id': str(pay['request_id']),
                        'department': pay['department']
                    })

                return result

    except Exception as e:
        print(f"Error getting student payments: {e}")
        return []

# ============================================================================
# TRANSCRIPT FILE FUNCTIONS
# ============================================================================

def add_transcript(request_id, student_reg_no, student_name, academic_years, filename):
    """Add transcript file - replaces old function"""
    print(f"🔄 add_transcript called with:")
    print(f"   - request_id: {request_id}")
    print(f"   - student_reg_no: {student_reg_no}")
    print(f"   - student_name: {student_name}")
    print(f"   - academic_years: {academic_years}")
    print(f"   - filename: {filename}")

    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # First, verify the request exists and get student info
                print(f"🔍 Verifying request {request_id} exists...")
                cursor.execute("""
                    SELECT tr.id, tr.student_id, u.reg_no, u.name
                    FROM new_transcript_requests tr
                    JOIN students s ON tr.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    WHERE tr.id = %s
                """, (request_id,))

                request_result = cursor.fetchone()
                if not request_result:
                    print(f"❌ Request {request_id} not found in database")
                    return False

                db_student_id = request_result[1]
                db_reg_no = request_result[2]
                db_student_name = request_result[3]

                print(f"✅ Found request {request_id}")
                print(f"   - Database student_id: {db_student_id}")
                print(f"   - Database reg_no: {db_reg_no}")
                print(f"   - Database student_name: {db_student_name}")
                print(f"   - Provided reg_no: {student_reg_no}")

                # Verify the student matches
                if db_reg_no != student_reg_no:
                    print(f"⚠️ Warning: reg_no mismatch. DB: {db_reg_no}, Provided: {student_reg_no}")
                    print(f"   Using database reg_no: {db_reg_no}")

                # Insert transcript file record
                print(f"🔄 Inserting transcript file record...")
                cursor.execute("""
                    INSERT INTO transcript_files
                    (request_id, student_id, filename, original_filename, file_path,
                     file_size, mime_type, academic_years, uploaded_by)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    request_id, db_student_id, filename, filename, f"static/uploads/transcripts/{filename}",
                    0, 'application/pdf', json.dumps(academic_years), 1  # Default uploader ID
                ))
                print(f"✅ Transcript file record inserted")

                # Update request status to completed
                print(f"🔄 Updating request status to completed...")
                current_time = datetime.now()
                cursor.execute("""
                    UPDATE new_transcript_requests
                    SET status = 'completed',
                        faculty_processed_at = %s,
                        completed_at = %s
                    WHERE id = %s
                """, (current_time, current_time, request_id))

                if cursor.rowcount > 0:
                    print(f"✅ Request status updated to completed for request {request_id}")
                else:
                    print(f"⚠️ No rows updated for request {request_id} - request might not exist")
                    return False

                connection.commit()
                print(f"✅ Transaction committed successfully")
                return True

    except Exception as e:
        print(f"❌ Error adding transcript: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

def get_transcripts_by_student_id(student_reg_no):
    """Get transcripts by student - replaces old function"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT tf.*, u.reg_no as student_id, u.name as student_name
                    FROM transcript_files tf
                    JOIN students s ON tf.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    WHERE u.reg_no = %s
                    ORDER BY tf.upload_date DESC
                """, (student_reg_no,))

                transcripts = cursor.fetchall()

                # Convert to old format
                result = []
                for trans in transcripts:
                    result.append({
                        'id': str(trans['request_id']),
                        'student_id': trans['student_id'],
                        'student_name': trans['student_name'],
                        'academic_years': json.loads(trans['academic_years']),
                        'filename': trans['filename'],
                        'upload_date': trans['upload_date'].strftime('%Y-%m-%d')
                    })

                return result

    except Exception as e:
        print(f"Error getting student transcripts: {e}")
        return []

def get_all_transcripts():
    """Get all transcripts - replaces old function"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT tf.*, u.reg_no as student_id, u.name as student_name
                    FROM transcript_files tf
                    JOIN students s ON tf.student_id = s.id
                    JOIN new_users u ON s.user_id = u.id
                    ORDER BY tf.upload_date DESC
                """)

                transcripts = cursor.fetchall()

                # Convert to old format
                result = []
                for trans in transcripts:
                    result.append({
                        'id': str(trans['request_id']),
                        'student_id': trans['student_id'],
                        'student_name': trans['student_name'],
                        'academic_years': json.loads(trans['academic_years']),
                        'filename': trans['filename'],
                        'upload_date': trans['upload_date'].strftime('%Y-%m-%d')
                    })

                return result

    except Exception as e:
        print(f"Error getting all transcripts: {e}")
        return []

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def get_database():
    """Get database data - replaces old function (for compatibility)"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Get all data in old format for compatibility
                data = {
                    'requests': get_all_requests(),
                    'transcripts': get_all_transcripts(),
                    'payments': [],
                    'department_fees': {}
                }

                # Get department fees
                cursor.execute("SELECT name, transcript_fee FROM departments")
                for name, fee in cursor.fetchall():
                    data['department_fees'][name] = {
                        'faculty': 'N/A',
                        'fee': float(fee)
                    }

                return data

    except Exception as e:
        print(f"Error getting database: {e}")
        return {'requests': [], 'transcripts': [], 'payments': [], 'department_fees': {}}

def save_database(data):
    """Save database - no longer needed but kept for compatibility"""
    # This function is no longer needed as we use direct database operations
    # But kept for compatibility with existing code
    pass

def test_connection():
    """Test database connection"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM new_users")
                count = cursor.fetchone()[0]
                print(f"✅ Database connection successful. Found {count} users.")
                return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def ensure_finance_users_exist():
    """Ensure finance users exist in the database for email notifications"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # Check if any finance users exist
                cursor.execute("SELECT COUNT(*) FROM new_users WHERE role = 'finance' AND is_active = 1")
                finance_count = cursor.fetchone()[0]

                print(f"🔍 Found {finance_count} finance users in database")

                if finance_count == 0:
                    print("🔧 No finance users found. Adding default finance users...")

                    # Add default finance users
                    default_finance_users = [
                        {
                            'name': 'Finance Office',
                            'email': '<EMAIL>',
                            'reg_no': 'FIN001',
                            'role': 'finance',
                            'password': bcrypt.hashpw('finance123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                        },
                        {
                            'name': 'Finance Administrator',
                            'email': '<EMAIL>',
                            'reg_no': 'FIN002',
                            'role': 'finance',
                            'password': bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                        }
                    ]

                    for user in default_finance_users:
                        try:
                            cursor.execute("""
                                INSERT INTO new_users (name, email, reg_no, role, password_hash, is_active, created_at)
                                VALUES (%s, %s, %s, %s, %s, 1, %s)
                                ON DUPLICATE KEY UPDATE
                                role = VALUES(role), is_active = 1
                            """, (
                                user['name'], user['email'], user['reg_no'],
                                user['role'], user['password'], datetime.now()
                            ))
                            print(f"✅ Added/Updated finance user: {user['name']} ({user['email']})")
                        except Exception as user_error:
                            print(f"❌ Error adding finance user {user['email']}: {user_error}")

                    connection.commit()
                    print("✅ Finance users setup completed")
                else:
                    print("✅ Finance users already exist in database")

                return True

    except Exception as e:
        print(f"❌ Error ensuring finance users exist: {e}")
        return False
