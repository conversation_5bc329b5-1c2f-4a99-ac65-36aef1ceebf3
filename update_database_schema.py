#!/usr/bin/env python3
"""
Database schema update script for INES Transcript System
This script will add all missing columns to make the system work properly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pymysql
from new_database_service import DB_CONFIG

def update_database_schema():
    """Update database schema with missing columns"""
    print("🔧 Starting database schema update...")
    
    try:
        # Connect to database
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Connected to database successfully")
        
        with connection.cursor() as cursor:
            # List of columns to add
            columns_to_add = [
                {
                    'name': 'payment_proof_filename',
                    'definition': 'VARCHAR(255) NULL',
                    'description': 'Payment proof file name'
                },
                {
                    'name': 'approved_date',
                    'definition': 'DATETIME NULL',
                    'description': 'Date when request was approved'
                },
                {
                    'name': 'rejected_at',
                    'definition': 'DATETIME NULL',
                    'description': 'Date when request was rejected'
                },
                {
                    'name': 'rejection_reason',
                    'definition': 'TEXT NULL',
                    'description': 'Reason for rejection'
                },
                {
                    'name': 'finance_approved_by',
                    'definition': 'VARCHAR(50) NULL',
                    'description': 'Finance user who approved'
                },
                {
                    'name': 'finance_rejected_by',
                    'definition': 'VARCHAR(50) NULL',
                    'description': 'Finance user who rejected'
                }
            ]
            
            # Check existing columns first
            cursor.execute("DESCRIBE new_transcript_requests")
            existing_columns = [row[0] for row in cursor.fetchall()]
            print(f"📋 Found {len(existing_columns)} existing columns")
            
            # Add missing columns
            for column in columns_to_add:
                if column['name'] not in existing_columns:
                    try:
                        sql = f"ALTER TABLE new_transcript_requests ADD COLUMN {column['name']} {column['definition']}"
                        cursor.execute(sql)
                        print(f"✅ Added column: {column['name']} - {column['description']}")
                    except Exception as e:
                        print(f"⚠️ Could not add {column['name']}: {e}")
                else:
                    print(f"ℹ️ Column {column['name']} already exists")
            
            # Commit changes
            connection.commit()
            print("✅ Database schema updated successfully!")
            
            # Verify the updates
            cursor.execute("DESCRIBE new_transcript_requests")
            updated_columns = [row[0] for row in cursor.fetchall()]
            print(f"📋 Database now has {len(updated_columns)} columns")
            
            return True
            
    except Exception as e:
        print(f"❌ Database update failed: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()

def create_upload_directory():
    """Create upload directory for payment proofs"""
    print("\n📁 Creating upload directories...")
    
    try:
        import os
        
        # Create main upload directory
        upload_dir = os.path.join('static', 'uploads')
        os.makedirs(upload_dir, exist_ok=True)
        print(f"✅ Created directory: {upload_dir}")
        
        # Create payment proofs directory
        proof_dir = os.path.join(upload_dir, 'payment_proofs')
        os.makedirs(proof_dir, exist_ok=True)
        print(f"✅ Created directory: {proof_dir}")
        
        # Create transcripts directory
        transcript_dir = os.path.join(upload_dir, 'transcripts')
        os.makedirs(transcript_dir, exist_ok=True)
        print(f"✅ Created directory: {transcript_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Directory creation failed: {e}")
        return False

def insert_sample_data():
    """Insert sample data for testing"""
    print("\n📊 Inserting sample data...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        
        with connection.cursor() as cursor:
            # Check if we have students
            cursor.execute("SELECT COUNT(*) FROM students")
            student_count = cursor.fetchone()[0]
            
            if student_count == 0:
                print("⚠️ No students found. Please ensure you have student data in the database.")
                return False
            
            print(f"✅ Found {student_count} students in database")
            
            # Insert sample school fees data
            cursor.execute("SELECT COUNT(*) FROM school_fees_payments")
            fees_count = cursor.fetchone()[0]
            
            if fees_count == 0:
                print("📊 Adding sample school fees data...")
                
                # Get first few students
                cursor.execute("SELECT id FROM students LIMIT 5")
                student_ids = [row[0] for row in cursor.fetchall()]
                
                for student_id in student_ids:
                    # Add fees for 2023-2024
                    cursor.execute("""
                        INSERT IGNORE INTO school_fees_payments 
                        (student_id, academic_year, department, total_fees_required, amount_paid, payment_status)
                        VALUES (%s, '2023-2024', 'Computer Science', 750000.00, 750000.00, 'paid')
                    """, (student_id,))
                    
                    # Add fees for 2022-2023
                    cursor.execute("""
                        INSERT IGNORE INTO school_fees_payments 
                        (student_id, academic_year, department, total_fees_required, amount_paid, payment_status)
                        VALUES (%s, '2022-2023', 'Computer Science', 700000.00, 700000.00, 'paid')
                    """, (student_id,))
                
                connection.commit()
                print("✅ Sample school fees data added")
            else:
                print(f"ℹ️ School fees data already exists ({fees_count} records)")
            
            return True
            
    except Exception as e:
        print(f"❌ Sample data insertion failed: {e}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()

def test_system():
    """Test the system after updates"""
    print("\n🧪 Testing system functionality...")
    
    try:
        from new_database_service import get_finance_dashboard_data, get_requests_by_student_id
        
        # Test finance dashboard
        print("🔍 Testing finance dashboard...")
        dashboard_data = get_finance_dashboard_data()
        print(f"✅ Finance dashboard working - {dashboard_data['pending_count']} pending requests")
        
        # Test student requests
        print("🔍 Testing student requests...")
        # Get first student
        connection = pymysql.connect(**DB_CONFIG)
        with connection.cursor() as cursor:
            cursor.execute("SELECT u.reg_no FROM new_users u JOIN students s ON u.id = s.user_id LIMIT 1")
            result = cursor.fetchone()
            if result:
                student_id = result[0]
                requests = get_requests_by_student_id(student_id)
                print(f"✅ Student requests working - {len(requests)} requests for {student_id}")
            else:
                print("⚠️ No students found for testing")
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False

def main():
    """Run all setup tasks"""
    print("🚀 INES Transcript System Setup")
    print("=" * 50)
    
    # Step 1: Update database schema
    schema_success = update_database_schema()
    
    # Step 2: Create directories
    dir_success = create_upload_directory()
    
    # Step 3: Insert sample data
    data_success = insert_sample_data()
    
    # Step 4: Test system
    test_success = test_system()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SETUP SUMMARY:")
    print(f"   Database Schema: {'✅ UPDATED' if schema_success else '❌ FAILED'}")
    print(f"   Upload Directories: {'✅ CREATED' if dir_success else '❌ FAILED'}")
    print(f"   Sample Data: {'✅ INSERTED' if data_success else '❌ FAILED'}")
    print(f"   System Test: {'✅ PASSED' if test_success else '❌ FAILED'}")
    
    if all([schema_success, dir_success, data_success, test_success]):
        print("\n🎉 SYSTEM READY! You can now start making transcript requests.")
        print("\n📋 Next steps:")
        print("   1. Start the Flask application: python app.py")
        print("   2. Login as a student")
        print("   3. Go to Request Transcript")
        print("   4. Fill the form and upload payment proof")
        print("   5. Check the request appears in View Status")
        print("   6. Login as finance to approve/reject")
        return True
    else:
        print("\n⚠️ Some setup steps failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
