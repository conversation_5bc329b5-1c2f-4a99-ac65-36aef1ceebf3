{% extends "student/base.html" %}

{% block title %}{{ translations.view_downloads or 'View Downloads' }}{% endblock %}

{% block content %}
<style>
    .dashboard-header {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        text-align: center;
    }
    .dashboard-header h1 {
        color: #333;
        font-size: 2rem;
        margin-bottom: 10px;
        font-weight: 700;
    }
    .dashboard-header p {
        color: #666;
        font-size: 1.1rem;
        margin: 0;
    }
    .downloads-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    .downloads-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .downloads-table th {
        background: #f8f9fa;
        color: #333;
        padding: 15px 12px;
        text-align: left;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
        font-size: 0.9rem;
    }
    .downloads-table td {
        padding: 15px 12px;
        border-bottom: 1px solid #e9ecef;
        color: #495057;
        vertical-align: middle;
    }
    .downloads-table tr:hover {
        background: #f8f9fa;
    }
    .download-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }
    .download-btn:hover {
        background: #1e7e34;
        color: white;
        text-decoration: none;
    }
    .download-btn:disabled {
        background: #6c757d;
        cursor: not-allowed;
    }
    .download-btn img {
        width: 16px;
        height: 16px;
        margin-right: 6px;
    }
    .status-badge {
        padding: 8px 14px;
        border-radius: 20px;
        font-size: 0.95rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    .status-ready {
        background: #d4edda;
        color: #155724;
    }
    .status-downloaded {
        background: #cce5ff;
        color: #004085;
    }
    .no-downloads {
        text-align: center;
        padding: 40px;
        color: #666;
    }
    .no-downloads i {
        font-size: 3rem;
        color: #ccc;
        margin-bottom: 15px;
    }
    .download-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
        font-size: 0.9rem;
        color: #666;
    }
    .download-info h4 {
        color: #333;
        font-size: 1rem;
        margin-bottom: 10px;
        font-weight: 600;
    }

    /* Delete button styling */
    .download-btn[style*="background-color: #dc3545"]:hover {
        background-color: #c82333 !important;
        border-color: #bd2130 !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(220,53,69,0.3);
    }

    .download-btn[style*="background-color: #dc3545"] {
        transition: all 0.3s ease;
    }
</style>

<div class="dashboard-header">
    <h1>{{ translations.view_downloads or 'View Downloads' }}</h1>
    <p>{{ translations.download_desc or 'Download your approved transcript requests' }}</p>
</div>

<div class="downloads-card">
    {% if approved_requests %}
    <div class="table-responsive">
        <table class="downloads-table">
            <thead>
                <tr>
                    <th>{{ translations.request_id }}</th>
                    <th>{{ translations.academic_years }}</th>
                    <th>{{ translations.date_approved or 'Date Approved' }}</th>
                    <th>{{ translations.status }}</th>
                    <th>{{ translations.download_count or 'Downloads' }}</th>
                    <th>{{ translations.action }}</th>
                </tr>
            </thead>
            <tbody>
                {% for request in approved_requests %}
                <tr>
                    <td><strong>{{ request.id }}</strong></td>
                    <td>{{ request.academic_years|join(', ') if request.academic_years else 'N/A' }}</td>
                    <td>{{ request.approved_date or request.date }}</td>
                    <td>
                        {% if request.status == 'done' %}
                            <span class="status-badge status-downloaded">
                                <img src="{{ url_for('static', filename='images/done.png') }}" alt="Downloaded" style="width: 16px; height: 16px; vertical-align: middle;">
                                {{ translations.downloaded or 'Downloaded' }}
                            </span>
                        {% elif request.status == 'completed' %}
                            <span class="status-badge status-ready">
                                <img src="{{ url_for('static', filename='images/8.png') }}" alt="Ready" style="width: 16px; height: 16px; vertical-align: middle;">
                                {{ translations.ready_download or 'Ready for Download' }}
                            </span>
                        {% else %}
                            <span class="status-badge status-pending">
                                <img src="{{ url_for('static', filename='images/13.png') }}" alt="Processing" style="width: 16px; height: 16px; vertical-align: middle;">
                                {{ translations.processing or 'Processing' }}
                            </span>
                        {% endif %}
                    </td>
                    <td>
                        {% if request.status == 'done' %}
                            1 {{ translations.times or 'time' }}
                            {% if request.last_downloaded %}
                                <br><small style="color: #666;">{{ translations.last_downloaded or 'Downloaded' }}: {{ request.last_downloaded }}</small>
                            {% endif %}
                        {% else %}
                            <span style="color: #999;">{{ translations.not_downloaded or 'Not downloaded yet' }}</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if request.status == 'done' %}
                            <!-- Delete button for downloaded transcripts -->
                            <form method="POST" action="{{ url_for('student_delete_downloaded_request', request_id=request.id) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this downloaded transcript from your list? This action cannot be undone.')">
                                <button type="submit" class="download-btn" style="background-color: #dc3545; border-color: #dc3545;" title="Delete downloaded transcript">
                                    <img src="{{ url_for('static', filename='images/rejected.png') }}" alt="Delete" style="width: 16px; height: 16px;">
                                    {{ translations.delete or 'Delete' }}
                                </button>
                            </form>
                        {% elif request.status == 'completed' %}
                            <!-- Download button for ready transcripts -->
                            <a href="{{ url_for('download_transcript', request_id=request.id) }}" class="download-btn">
                                <img src="{{ url_for('static', filename='images/8.png') }}" alt="Download">
                                {{ translations.download or 'Download' }}
                            </a>
                        {% else %}
                            <span style="color: #999;">{{ translations.not_available or 'Not available' }}</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Download Information -->
    <div class="download-info">
        <h4>{{ translations.download_info or 'Download Information' }}</h4>
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>📥 Download:</strong> {{ translations.download_info_1 or 'Click the download button to get your transcript as a PDF file' }}</li>
            <li><strong>🔒 Security:</strong> {{ translations.download_info_2 or 'Each transcript can only be downloaded once for security reasons' }}</li>
            <li><strong>🗑️ Auto-Remove:</strong> {{ translations.download_info_3 or 'After downloading, the request is automatically removed from your account' }}</li>
            <li><strong>💾 Save Copy:</strong> {{ translations.download_info_4 or 'Make sure to save the downloaded file in a secure location' }}</li>
            <li><strong>🔄 Need Another Copy?:</strong> {{ translations.download_info_5 or 'Submit a new transcript request if you need additional copies' }}</li>
            <li><strong>❓ Issues?:</strong> {{ translations.download_info_6 or 'Contact the administration office if you encounter any problems' }}</li>
        </ul>
    </div>
    {% else %}
    <div class="no-downloads">
        <i class="fas fa-download"></i>
        <p>{{ translations.no_approved_requests or 'No transcripts available for download' }}</p>
        <p style="color: #999; font-size: 0.9rem;">{{ translations.no_downloads_desc or 'Once your transcript requests are approved by faculty, they will appear here for download. After downloading, requests are automatically removed.' }}</p>
        <a href="{{ url_for('request_transcript') }}" class="download-btn" style="margin-top: 15px;">
            <i class="fas fa-plus" style="margin-right: 6px;"></i>
            {{ translations.request_transcript }}
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}
